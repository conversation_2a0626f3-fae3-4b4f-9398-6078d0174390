-- My Dog in Fit Database Schema for Supabase (PostgreSQL)
-- This schema supports the core features of dog health and fitness tracking

-- ============================================================================
-- CORE TABLES
-- ============================================================================

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- User profiles (extends Supabase auth.users)
CREATE TABLE user_profiles (
    id UUID REFERENCES auth.users(id) PRIMARY KEY,
    email TEXT,
    provider TEXT, -- 'email', 'google', 'apple'
    notifications_enabled BOOLEAN DEFAULT false,
    unit_system TEXT DEFAULT 'metric' CHECK (unit_system IN ('metric', 'imperial')),
    preferred_language TEXT DEFAULT 'en' CHECK (preferred_language IN ('en', 'ru', 'es')),
    theme TEXT DEFAULT 'light' CHECK (theme IN ('light', 'dark', 'colored')),
    onboarding_completed BOOLEAN DEFAULT false,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Dog breeds reference data
CREATE TABLE breeds (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name TEXT NOT NULL UNIQUE,
    slug TEXT NOT NULL UNIQUE,
    image_url TEXT,
    size_category TEXT CHECK (size_category IN ('small', 'medium', 'large')),
    -- Weight ranges for males/females (in kg)
    male_weight_min DECIMAL(4,1),
    male_weight_max DECIMAL(4,1),
    female_weight_min DECIMAL(4,1),
    female_weight_max DECIMAL(4,1),
    typical_activity_duration INTEGER, -- minutes per day
    preferred_activities TEXT[], -- array of activity types
    description TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Dog profiles
CREATE TABLE dogs (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES user_profiles(id) ON DELETE CASCADE NOT NULL,
    name TEXT NOT NULL,
    breed_id UUID REFERENCES breeds(id),
    custom_breed TEXT, -- for mixed breeds or unlisted breeds
    gender TEXT CHECK (gender IN ('male', 'female')),
    birthday DATE,
    image_url TEXT,
    size_category TEXT CHECK (size_category IN ('small', 'medium', 'large')),
    typical_activity TEXT CHECK (typical_activity IN ('walking', 'running', 'mixed')),
    typical_activity_duration INTEGER, -- minutes per day
    daily_calorie_goal INTEGER,
    daily_activity_goal INTEGER, -- minutes
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- ============================================================================
-- HEALTH & METRICS TABLES
-- ============================================================================

-- Weight tracking
CREATE TABLE weight_logs (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    dog_id UUID REFERENCES dogs(id) ON DELETE CASCADE NOT NULL,
    weight DECIMAL(5,2) NOT NULL, -- in kg, supports 0.01 to 999.99
    date DATE NOT NULL,
    notes TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(dog_id, date) -- one weight entry per dog per day
);

-- Body Condition Score tracking
CREATE TABLE bcs_logs (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    dog_id UUID REFERENCES dogs(id) ON DELETE CASCADE NOT NULL,
    bcs_score INTEGER CHECK (bcs_score >= 1 AND bcs_score <= 9) NOT NULL,
    date DATE NOT NULL,
    notes TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(dog_id, date) -- one BCS entry per dog per day
);

-- ============================================================================
-- ACTIVITY TRACKING TABLES
-- ============================================================================

-- Activity sessions
CREATE TABLE activities (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    dog_id UUID REFERENCES dogs(id) ON DELETE CASCADE NOT NULL,
    activity_type TEXT CHECK (activity_type IN ('walking', 'running', 'mixed')) NOT NULL,
    start_time TIMESTAMPTZ NOT NULL,
    end_time TIMESTAMPTZ,
    duration_minutes INTEGER, -- calculated field
    calories_burned INTEGER,
    distance_meters DECIMAL(8,2), -- optional distance tracking
    notes TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- ============================================================================
-- NUTRITION TABLES
-- ============================================================================

-- Food database
CREATE TABLE foods (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name TEXT NOT NULL,
    brand TEXT NOT NULL,
    food_type TEXT CHECK (food_type IN ('dry', 'wet', 'raw', 'treat', 'supplement')),
    calories_per_100g INTEGER NOT NULL,
    calories_per_cup INTEGER, -- for dry food
    protein_percentage DECIMAL(4,1),
    fat_percentage DECIMAL(4,1),
    carb_percentage DECIMAL(4,1),
    fiber_percentage DECIMAL(4,1),
    image_url TEXT,
    barcode TEXT, -- for food label scanning
    is_verified BOOLEAN DEFAULT false, -- verified by admin
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(name, brand)
);

-- Dog's preferred foods
CREATE TABLE dog_preferred_foods (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    dog_id UUID REFERENCES dogs(id) ON DELETE CASCADE NOT NULL,
    food_id UUID REFERENCES foods(id) ON DELETE CASCADE NOT NULL,
    started_date DATE DEFAULT CURRENT_DATE,
    ended_date DATE,
    is_current BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(dog_id, food_id, started_date)
);

-- Meal logging
CREATE TABLE meals (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    dog_id UUID REFERENCES dogs(id) ON DELETE CASCADE NOT NULL,
    meal_time TIMESTAMPTZ NOT NULL,
    meal_type TEXT CHECK (meal_type IN ('breakfast', 'lunch', 'dinner', 'snack', 'treat')),
    total_calories INTEGER DEFAULT 0,
    notes TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Meal items (foods within a meal)
CREATE TABLE meal_items (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    meal_id UUID REFERENCES meals(id) ON DELETE CASCADE NOT NULL,
    food_id UUID REFERENCES foods(id) NOT NULL,
    quantity_grams DECIMAL(6,2), -- weight of food
    quantity_cups DECIMAL(4,2), -- cups of food (for dry food)
    calories INTEGER NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- ============================================================================
-- AI ASSISTANT & RECOMMENDATIONS
-- ============================================================================

-- AI recommendations and tips
CREATE TABLE recommendations (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    dog_id UUID REFERENCES dogs(id) ON DELETE CASCADE NOT NULL,
    recommendation_type TEXT CHECK (recommendation_type IN ('nutrition', 'exercise', 'health', 'general')) NOT NULL,
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    priority INTEGER DEFAULT 1 CHECK (priority >= 1 AND priority <= 5),
    is_read BOOLEAN DEFAULT false,
    is_dismissed BOOLEAN DEFAULT false,
    valid_until DATE, -- optional expiration
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- AI chat history
CREATE TABLE ai_chat (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES user_profiles(id) ON DELETE CASCADE NOT NULL,
    dog_id UUID REFERENCES dogs(id) ON DELETE CASCADE, -- optional, for dog-specific queries
    message_type TEXT CHECK (message_type IN ('user', 'assistant')) NOT NULL,
    content TEXT NOT NULL,
    context_data JSONB, -- additional context for AI responses
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- ============================================================================
-- INDEXES FOR PERFORMANCE
-- ============================================================================

-- User and dog lookups
CREATE INDEX idx_dogs_user_id ON dogs(user_id);
CREATE INDEX idx_dogs_active ON dogs(user_id, is_active);

-- Time-based queries
CREATE INDEX idx_weight_logs_dog_date ON weight_logs(dog_id, date DESC);
CREATE INDEX idx_bcs_logs_dog_date ON bcs_logs(dog_id, date DESC);
CREATE INDEX idx_activities_dog_time ON activities(dog_id, start_time DESC);
CREATE INDEX idx_meals_dog_time ON meals(dog_id, meal_time DESC);

-- Food and nutrition
CREATE INDEX idx_foods_name_brand ON foods(name, brand);
CREATE INDEX idx_foods_barcode ON foods(barcode) WHERE barcode IS NOT NULL;
CREATE INDEX idx_dog_preferred_foods_current ON dog_preferred_foods(dog_id, is_current);

-- AI features
CREATE INDEX idx_recommendations_dog_unread ON recommendations(dog_id, is_read, created_at DESC);
CREATE INDEX idx_ai_chat_user_time ON ai_chat(user_id, created_at DESC);

-- ============================================================================
-- ROW LEVEL SECURITY (RLS) POLICIES
-- ============================================================================

-- Enable RLS on all tables
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE dogs ENABLE ROW LEVEL SECURITY;
ALTER TABLE weight_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE bcs_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE activities ENABLE ROW LEVEL SECURITY;
ALTER TABLE dog_preferred_foods ENABLE ROW LEVEL SECURITY;
ALTER TABLE meals ENABLE ROW LEVEL SECURITY;
ALTER TABLE meal_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE recommendations ENABLE ROW LEVEL SECURITY;
ALTER TABLE ai_chat ENABLE ROW LEVEL SECURITY;

-- User profiles: users can only access their own profile
CREATE POLICY "Users can view own profile" ON user_profiles FOR SELECT USING (auth.uid() = id);
CREATE POLICY "Users can update own profile" ON user_profiles FOR UPDATE USING (auth.uid() = id);
CREATE POLICY "Users can insert own profile" ON user_profiles FOR INSERT WITH CHECK (auth.uid() = id);

-- Dogs: users can only access their own dogs
CREATE POLICY "Users can view own dogs" ON dogs FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can create own dogs" ON dogs FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own dogs" ON dogs FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own dogs" ON dogs FOR DELETE USING (auth.uid() = user_id);

-- Weight logs: users can only access logs for their dogs
CREATE POLICY "Users can view weight logs for own dogs" ON weight_logs FOR SELECT
    USING (EXISTS (SELECT 1 FROM dogs WHERE dogs.id = weight_logs.dog_id AND dogs.user_id = auth.uid()));
CREATE POLICY "Users can create weight logs for own dogs" ON weight_logs FOR INSERT
    WITH CHECK (EXISTS (SELECT 1 FROM dogs WHERE dogs.id = weight_logs.dog_id AND dogs.user_id = auth.uid()));
CREATE POLICY "Users can update weight logs for own dogs" ON weight_logs FOR UPDATE
    USING (EXISTS (SELECT 1 FROM dogs WHERE dogs.id = weight_logs.dog_id AND dogs.user_id = auth.uid()));
CREATE POLICY "Users can delete weight logs for own dogs" ON weight_logs FOR DELETE
    USING (EXISTS (SELECT 1 FROM dogs WHERE dogs.id = weight_logs.dog_id AND dogs.user_id = auth.uid()));

-- BCS logs: similar to weight logs
CREATE POLICY "Users can view bcs logs for own dogs" ON bcs_logs FOR SELECT
    USING (EXISTS (SELECT 1 FROM dogs WHERE dogs.id = bcs_logs.dog_id AND dogs.user_id = auth.uid()));
CREATE POLICY "Users can create bcs logs for own dogs" ON bcs_logs FOR INSERT
    WITH CHECK (EXISTS (SELECT 1 FROM dogs WHERE dogs.id = bcs_logs.dog_id AND dogs.user_id = auth.uid()));
CREATE POLICY "Users can update bcs logs for own dogs" ON bcs_logs FOR UPDATE
    USING (EXISTS (SELECT 1 FROM dogs WHERE dogs.id = bcs_logs.dog_id AND dogs.user_id = auth.uid()));
CREATE POLICY "Users can delete bcs logs for own dogs" ON bcs_logs FOR DELETE
    USING (EXISTS (SELECT 1 FROM dogs WHERE dogs.id = bcs_logs.dog_id AND dogs.user_id = auth.uid()));

-- Activities: users can only access activities for their dogs
CREATE POLICY "Users can view activities for own dogs" ON activities FOR SELECT
    USING (EXISTS (SELECT 1 FROM dogs WHERE dogs.id = activities.dog_id AND dogs.user_id = auth.uid()));
CREATE POLICY "Users can create activities for own dogs" ON activities FOR INSERT
    WITH CHECK (EXISTS (SELECT 1 FROM dogs WHERE dogs.id = activities.dog_id AND dogs.user_id = auth.uid()));
CREATE POLICY "Users can update activities for own dogs" ON activities FOR UPDATE
    USING (EXISTS (SELECT 1 FROM dogs WHERE dogs.id = activities.dog_id AND dogs.user_id = auth.uid()));
CREATE POLICY "Users can delete activities for own dogs" ON activities FOR DELETE
    USING (EXISTS (SELECT 1 FROM dogs WHERE dogs.id = activities.dog_id AND dogs.user_id = auth.uid()));

-- Food database: readable by all authenticated users
CREATE POLICY "All users can view foods" ON foods FOR SELECT TO authenticated USING (true);

-- Dog preferred foods: users can only access for their dogs
CREATE POLICY "Users can view preferred foods for own dogs" ON dog_preferred_foods FOR SELECT
    USING (EXISTS (SELECT 1 FROM dogs WHERE dogs.id = dog_preferred_foods.dog_id AND dogs.user_id = auth.uid()));
CREATE POLICY "Users can manage preferred foods for own dogs" ON dog_preferred_foods FOR ALL
    USING (EXISTS (SELECT 1 FROM dogs WHERE dogs.id = dog_preferred_foods.dog_id AND dogs.user_id = auth.uid()));

-- Meals and meal items: users can only access for their dogs
CREATE POLICY "Users can view meals for own dogs" ON meals FOR SELECT
    USING (EXISTS (SELECT 1 FROM dogs WHERE dogs.id = meals.dog_id AND dogs.user_id = auth.uid()));
CREATE POLICY "Users can manage meals for own dogs" ON meals FOR ALL
    USING (EXISTS (SELECT 1 FROM dogs WHERE dogs.id = meals.dog_id AND dogs.user_id = auth.uid()));

CREATE POLICY "Users can view meal items for own dogs" ON meal_items FOR SELECT
    USING (EXISTS (
        SELECT 1 FROM meals
        JOIN dogs ON dogs.id = meals.dog_id
        WHERE meals.id = meal_items.meal_id AND dogs.user_id = auth.uid()
    ));
CREATE POLICY "Users can manage meal items for own dogs" ON meal_items FOR ALL
    USING (EXISTS (
        SELECT 1 FROM meals
        JOIN dogs ON dogs.id = meals.dog_id
        WHERE meals.id = meal_items.meal_id AND dogs.user_id = auth.uid()
    ));

-- Recommendations: users can only access for their dogs
CREATE POLICY "Users can view recommendations for own dogs" ON recommendations FOR SELECT
    USING (EXISTS (SELECT 1 FROM dogs WHERE dogs.id = recommendations.dog_id AND dogs.user_id = auth.uid()));
CREATE POLICY "Users can update recommendations for own dogs" ON recommendations FOR UPDATE
    USING (EXISTS (SELECT 1 FROM dogs WHERE dogs.id = recommendations.dog_id AND dogs.user_id = auth.uid()));

-- AI chat: users can only access their own chat
CREATE POLICY "Users can view own ai chat" ON ai_chat FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can create own ai chat" ON ai_chat FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Breeds table: readable by all authenticated users (reference data)
ALTER TABLE breeds ENABLE ROW LEVEL SECURITY;
CREATE POLICY "All users can view breeds" ON breeds FOR SELECT TO authenticated USING (true);

-- ============================================================================
-- FUNCTIONS AND TRIGGERS FOR AUTOMATION
-- ============================================================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Add updated_at triggers to tables that need them
CREATE TRIGGER update_user_profiles_updated_at BEFORE UPDATE ON user_profiles
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_dogs_updated_at BEFORE UPDATE ON dogs
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_breeds_updated_at BEFORE UPDATE ON breeds
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_foods_updated_at BEFORE UPDATE ON foods
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to calculate activity duration
CREATE OR REPLACE FUNCTION calculate_activity_duration()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.end_time IS NOT NULL THEN
        NEW.duration_minutes = EXTRACT(EPOCH FROM (NEW.end_time - NEW.start_time))/60;
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger to auto-calculate activity duration
CREATE TRIGGER calculate_activity_duration_trigger
    BEFORE INSERT OR UPDATE ON activities
    FOR EACH ROW EXECUTE FUNCTION calculate_activity_duration();

-- Function to calculate meal total calories from items
CREATE OR REPLACE FUNCTION update_meal_calories()
RETURNS TRIGGER AS $$
BEGIN
    UPDATE meals
    SET total_calories = (
        SELECT COALESCE(SUM(calories), 0)
        FROM meal_items
        WHERE meal_id = COALESCE(NEW.meal_id, OLD.meal_id)
    )
    WHERE id = COALESCE(NEW.meal_id, OLD.meal_id);
    RETURN COALESCE(NEW, OLD);
END;
$$ language 'plpgsql';

-- Trigger to update meal calories when meal items change
CREATE TRIGGER update_meal_calories_trigger
    AFTER INSERT OR UPDATE OR DELETE ON meal_items
    FOR EACH ROW EXECUTE FUNCTION update_meal_calories();
