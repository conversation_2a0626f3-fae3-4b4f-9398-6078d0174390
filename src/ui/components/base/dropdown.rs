use dioxus_core::AttributeValue;
use dioxus_primitives::{
    dropdown_menu,
    dropdown_menu::DropdownMenuTrigger,
};

use crate::ui::prelude::*;


const DROPDOWN_MENU_CSS: Asset = asset!("/src/ui/components/base/dropdown.css");

/// The props for the [`DropdownMenu`] component
#[derive(Props, Clone, PartialEq)]
pub struct DropdownMenuProps {
    /// Whether the dropdown menu is open. If not provided, the component will be uncontrolled and use
    /// `default_open`.
    pub open: ReadOnlySignal<Option<bool>>,

    // /// Default open state if the component is not controlled.
    // #[props(default)]
    // pub default_open: bool,

    // /// Callback when the open state changes. This is called when the dropdown menu is opened or closed.
    // #[props(default)]
    // pub on_open_change: Callback<bool>,
    /// Whether the dropdown menu is disabled. If true, the menu will not open and items will not be
    /// selectable.
    #[props(default)]
    pub disabled: ReadOnlySignal<bool>,

    /// Whether focus should loop around when reaching the end.
    // #[props(default = ReadOnlySignal::new(Signal::new(true)))]
    // pub roving_loop: ReadOnlySignal<bool>,
    trigger: Element,

    /// Additional attributes to apply to the dropdown menu element.
    #[props(extends = GlobalAttributes)]
    attributes: Vec<Attribute>,

    #[props(default)]
    content_class: String,

    /// The children of the dropdown menu, which should include a [`DropdownMenuTrigger`] and a
    /// [`DropdownMenuContent`].
    children: Element,
}


#[component]
pub fn DropdownMenu(props: DropdownMenuProps) -> Element {
    // match props.children.as_ref() {
    //     // DropdownMenuContent {
    //     //     ..
    //     // } => {},
    //     Element {
    //         name, ..
    //     } if name == "DropdownMenuContent" => {},
    //     _ =>
    //         return rsx! {
    //           div { "DropdownMenu `children` must be DropdownMenuContent" }
    //         },
    // }
    rsx! {
        document::Stylesheet { rel: "stylesheet", href: DROPDOWN_MENU_CSS }
        dropdown_menu::DropdownMenu {
            class: "dropdown-menu {get_class(&props.attributes)}",
            // default_open: props.default_open,
            // on_open_change: props.on_open_change,
            disabled: props.disabled,
            // roving_loop: props.roving_loop,
            attributes: props.attributes,
            DropdownMenuTrigger { class: "dropdown-menu-trigger", {props.trigger} }
            dropdown_menu::DropdownMenuContent { class: "dropdown-menu-content {props.content_class}", {props.children} }
                // {props.children}
        }
    }
}


// /// The props for the [`DropdownMenuTrigger`] component
// #[derive(Props, Clone, PartialEq)]
// pub struct DropdownMenuTriggerProps {
//     /// Additional attributes to apply to the trigger button element.
//     #[props(extends = GlobalAttributes)]
//     attributes: Vec<Attribute>,
//     /// The children of the trigger button
//     children:   Element,
// }

// #[component]
// pub fn DropdownMenuTrigger(props: DropdownMenuTriggerProps) -> Element {
//     rsx! {
//       dropdown_menu::DropdownMenuTrigger { class: "dropdown-menu-trigger", attributes: props.attributes,
// {props.children} }     }
// }


// /// The props for the [`DropdownMenuContent`] component
// #[derive(Props, Clone, PartialEq)]
// pub struct DropdownMenuContentProps {
//     // /// The ID of the dropdown menu content element. If not provided, a unique ID will be generated.
//     // pub id:     ReadOnlySignal<Option<String>>,
//     /// Additional attributes to apply to the dropdown menu content element.
//     #[props(extends = GlobalAttributes)]
//     attributes: Vec<Attribute>,
//     /// The children of the dropdown menu content, which should include one or more [`DropdownMenuItem`]
//     /// components.
//     children:   Element,
// }

// #[component]
// pub fn DropdownMenuContent(props: DropdownMenuContentProps) -> Element {
//     rsx! {
//       dropdown_menu::DropdownMenuContent {
//         class: "dropdown-menu-content {get_class(&props.attributes)}",
//         // id: props.id,
//         attributes: props.attributes,
//         {props.children}
//       }
//     }
// }


/// The props for the [`DropdownMenuItem`] component
#[derive(Props, Clone, PartialEq)]
pub struct DropdownMenuItemProps {
    /// The value of the item, which will be passed to the `on_select` callback when clicked.
    pub value: ReadOnlySignal<String>,
    /// The index of the item within the [`DropdownMenuContent`]. This is used to order the items for
    /// keyboard navigation.
    pub index: ReadOnlySignal<usize>,

    /// Whether the item is disabled. If true, the item will not be clickable and will not respond to
    /// keyboard events. Defaults to false.
    #[props(default)]
    pub disabled: ReadOnlySignal<bool>,

    /// The callback function that will be called when the item is selected. The value of the item will be
    /// passed as an argument.
    #[props(default)]
    pub on_select: Callback<String>,

    /// Additional attributes to apply to the item element.
    #[props(extends = GlobalAttributes)]
    attributes: Vec<Attribute>,
    /// The children of the item, which will be rendered inside the item element.
    children:   Element,
}

#[component]
pub fn DropdownMenuItem(props: DropdownMenuItemProps) -> Element {
    rsx! {
        document::Stylesheet { rel: "stylesheet", href: DROPDOWN_MENU_CSS }
        dropdown_menu::DropdownMenuItem {
            class: "dropdown-menu-item {get_class(&props.attributes)}",
            value: props.value,
            index: props.index,
            disabled: props.disabled,
            on_select: props.on_select,
            attributes: props.attributes,
            {props.children}
        }
    }
}


fn get_class(attributes: &[Attribute]) -> String {
    attributes
        .iter()
        .find(|a| a.name == "class" && matches!(a.value, AttributeValue::Text(_)))
        .map_or(String::new(), |a| match a.value.clone() {
            AttributeValue::Text(s) => s,
            _ => unreachable!(),
        })
}
