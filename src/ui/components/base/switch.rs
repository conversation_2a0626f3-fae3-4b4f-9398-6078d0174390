use dioxus_primitives::switch;

use crate::ui::prelude::*;


const SWITCH_CSS: Asset = asset!("/src/ui/components/base/switch.css");

#[derive(Props, PartialEq, Clone)]
pub struct SwitchProps {
    #[props(default)]
    pub checked:   bool,
    #[props(default)]
    pub on_change: EventHandler<bool>,
}

#[component]
pub fn Switch(props: SwitchProps) -> Element {
    let base_class = "peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full \
                      border-2 border-transparent transition-colors focus-visible:outline-none \
                      focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 \
                      focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50";
    let checked_class = "data-[state=checked]:bg-primary";
    let unchecked_class = "data-[state=unchecked]:bg-input";

    let thumb_base_class = "pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 \
                            transition-transform";
    let thumb_checked_class = "data-[state=checked]:translate-x-5";
    let thumb_unchecked_class = "data-[state=unchecked]:translate-x-0";

    let state = if props.checked { "checked" } else { "unchecked" };

    rsx! {
      // button {
      //   "type": "button",
      //   role: "switch",
      //   "aria-checked": props.checked.to_string(),
      //   "data-state": state,
      //   class: "{base_class} {checked_class} {unchecked_class}",
      //   onclick: move |_| props.on_change.call(!props.checked),
      //   span {
      //     "data-state": state,
      //     class: "{thumb_base_class} {thumb_checked_class} {thumb_unchecked_class}",
      //   }
      // }
      document::Stylesheet { rel: "stylesheet", href: SWITCH_CSS }
      switch::Switch {
        class: "switch",
        checked: props.checked,
        aria_label: "Switch",
        on_checked_change: move |new_checked| {
            props.on_change.call(new_checked);
        },
        switch::SwitchThumb { class: "switch-thumb" }
      }
    }
}
