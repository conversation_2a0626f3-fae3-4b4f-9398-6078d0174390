use crate::ui::prelude::*;


#[derive(<PERSON><PERSON><PERSON><PERSON>, <PERSON>q, <PERSON><PERSON>, <PERSON><PERSON>)]
pub enum ButtonVariant {
    Default,
    Destructive,
    Outline,
    Secondary,
    Ghost,
    Link,
    Glow,
    Glass,
}

#[derive(Pro<PERSON>, PartialEq, Clone)]
pub struct ButtonProps {
    #[props(default)]
    pub children: Element,
    #[props(default)]
    pub class:    String,
    #[props(optional)]
    pub color:    Option<String>,
    #[props(default = false)]
    pub disabled: bool,
    #[props(default)]
    pub onclick:  EventHandler<MouseEvent>,
    #[props(default = ButtonVariant::Default)]
    pub variant:  ButtonVariant,
    #[props(default)]
    pub size:     Size,
}


#[component]
pub fn Button(props: ButtonProps) -> Element {
    // let base_class = "inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm \
    //                   font-medium ring-offset-background transition-colors focus-visible:outline-none \
    //                   focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 \
    //                   disabled:pointer-events-none disabled:opacity-50";

    let variant_class = match props.variant {
        ButtonVariant::Destructive => "text-destructive-foreground",
        ButtonVariant::Outline => "border border-input hover:text-accent-foreground",
        ButtonVariant::Secondary => "text-secondary-foreground",
        ButtonVariant::Ghost => "hover:text-accent-foreground",
        ButtonVariant::Link => "text-primary underline-offset-4 hover:underline",
        ButtonVariant::Glow => "text-primary-foreground shadow-glow-sm hover:shadow-glow-md",
        ButtonVariant::Glass =>
            "backdrop-blur-md text-primary border border-white/20 dark-theme:text-white \
             dark-theme:border-white/10 colored-theme:text-white colored-theme:border-white/20",
        ButtonVariant::Default => "backdrop-blur-sm text-white hover:shadow-xs",
    };
    let bg_color = if let Some(color) = props.color.as_deref() {
        color
    } else {
        match props.variant {
            ButtonVariant::Destructive => "bg-destructive hover:bg-destructive/80",
            ButtonVariant::Glass =>
                "bg-white/70 dark-theme:bg-black/30 colored-theme:bg-white/20 hover:bg-white/80 \
                 dark-theme:hover:bg-black/40 colored-theme:hover:bg-white/30",
            ButtonVariant::Glow => "bg-primary hover:bg-primary/80",
            ButtonVariant::Link | ButtonVariant::Ghost => "bg-transparent",
            ButtonVariant::Outline => "bg-transparent hover:bg-accent/80",
            _ => "bg-secondary hover:bg-secondary/90",
        }
    };
    let scale_class = match props.variant {
        ButtonVariant::Secondary | ButtonVariant::Ghost | ButtonVariant::Link => "",
        _ => "active:scale-[0.90]",
    };

    let size_class = match props.size {
        Size::Sm => "h-9 rounded-xl px-3",
        Size::Lg => "h-11 px-8",
        // "icon" => "h-10 w-10",
        _ => "h-10 px-4 py-2", // default
    };

    rsx! {
        button {
            class: "flex items-center justify-center whitespace-nowrap rounded-full text-sm font-medium transition-all duration-300 focus-visible:outline-hidden ring-0 cursor-pointer disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none {variant_class} {size_class} {scale_class} {bg_color} {props.class}",
            onclick: move |evt| props.onclick.call(evt),
            {props.children}
        }
    }
}
