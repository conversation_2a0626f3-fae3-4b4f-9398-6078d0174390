.switch {
  all: unset;
  position: relative;
  width: 2rem;
  height: 1.15rem;
  border-radius: 9999px;
  background-color: var(--color-background-dark);
  cursor: pointer;
  transition: background-color 150ms;
}

.switch[data-state="checked"] {
  /* background-color: var(--color-secondary-foreground); */
  background-color: var(--color-secondary);
}

.switch-thumb {
  display: block;
  width: calc(1.15rem - 2px);
  height: calc(1.15rem - 2px);
  border-radius: 9999px;
  /* background-color: var(--light, var(--primary-color)) var(--dark, var(--secondary-color-2)); */
  background-color: var(--color-background);
  transform: translateX(1px);
  transition: transform 150ms;
  will-change: transform;
}

.switch[data-state="checked"] .switch-thumb {
  /* background-color: var(--light, var(--primary-color)) var(--dark, var(--primary-color-3)); */
  transform: translateX(calc(2rem - 1px - (1.15rem - 2px)));
}

/* Only apply disabled styles when data-disabled is "true" */
.switch[data-disabled="true"] {
  cursor: not-allowed;
  opacity: 0.5;
}
