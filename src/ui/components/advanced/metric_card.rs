use crate::ui::prelude::*;


#[component]
pub fn MetricCard(
    color: String,
    icon: IconRef,
    title: String,
    unit: String,
    value: String,
    class: Option<String>,
) -> Element {
    let bg_color = match color.as_str() {
        "red" => "bg-red/90",
        "teal" => "bg-pet-teal/95",
        "blue" => "bg-pet-blue/80",
        "purple" => "bg-pet-purple/70",
        "green" => "bg-pet-green/95",
        "yellow" => "bg-pet-yellow/90",
        "orange" => "bg-pet-orange/90",
        "pink" => "bg-pet-pink/90",
        _ => "bg-primary/35",
    };

    return rsx! {
      Card { class: "bg-background-dark/15 border-background-dark/25",
        // div { class: "px-4 py-3 flex items-center space-x-2",
        // div { class: "flex-1",
        p { class: "text-md text-black font-medium mb-2", "{title}" }
        Row { class: "justify-start space-x-2",
          Row { class: "w-10 h-10 justify-center rounded-full {bg_color} text-white",
            Icon { icon, size: Size::Sm }
          }
          div {
            span { class: "text-3xl font-semibold text-black/85", "{value}" }
            span { class: "text-sm font-semibold text-black/85", " {unit}" }
          }
        }
      }
    };
}
