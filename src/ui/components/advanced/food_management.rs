// use std::collections::HashMap;

// use dioxus::prelude::*;

// use crate::{
//     i18n::*,
//     models::*,
// };

// #[derive(Props, PartialEq, Clone)]
// pub struct FoodManagementProps {
//     pub pet_foods:         Vec<Food>,
//     #[props(optional)]
//     pub food_history:      Option<Vec<FoodHistoryEntry>>,
//     pub on_food_added:     EventHandler<Food>,
//     pub on_food_removed:   EventHandler<String>,
//     pub on_food_preferred: EventHandler<(String, bool, Option<String>)>, /* (food_id, is_preferred,
//                                                                           * start_date) */
// }

// #[component]
// pub fn FoodManagement(props: FoodManagementProps) -> Element {
//     let i18n = I18n::new();

//     let is_open = use_signal(|| false);
//     let selected_icon = use_signal(|| "beef".to_string());
//     let start_date = use_signal(|| chrono::Local::now().date_naive().to_string());

//     let new_food_name = use_signal(|| String::new());
//     let new_food_brand = use_signal(|| String::new());
//     let new_food_calories_per_cup = use_signal(|| 300);

//     // Mock food icons and brand logos
//     let food_icons: HashMap<&str, &str> = HashMap::from([
//         ("apple", "🍎"),
//         ("beef", "🥩"),
//         ("carrot", "🥕"),
//         ("fish", "🐟"),
//         ("milk", "🥛"),
//         ("wheat", "🌾"),
//         ("soup", "🥣"),
//         ("leafy-green", "🥬"),
//     ]);

//     let brand_logos: HashMap<&str, &str> = HashMap::from([
//         ("Royal Canin", "https://placehold.co/100x40?text=Royal+Canin"),
//         ("Purina", "https://placehold.co/100x40?text=Purina"),
//         ("Hill's", "https://placehold.co/100x40?text=Hills"),
//         ("Blue Buffalo", "https://placehold.co/100x40?text=Blue+Buffalo"),
//         ("Pedigree", "https://placehold.co/100x40?text=Pedigree"),
//         ("Merrick", "https://placehold.co/100x40?text=Merrick"),
//         ("Taste of the Wild", "https://placehold.co/100x40?text=ToTW"),
//         ("Wellness", "https://placehold.co/100x40?text=Wellness"),
//     ]);

//     let handle_submit = move |_| {
//         let new_food = Food {
//             id:               uuid::Uuid::new_v4().to_string(),
//             name:             new_food_name.get().clone(),
//             brand:            new_food_brand.get().clone(),
//             calories_per_cup: *new_food_calories_per_cup.get(),
//             icon:             selected_icon.get().clone(),
//             logo:             brand_logos
//                 .get(new_food_brand.get().as_str())
//                 .map(|s| s.to_string()),
//             is_preferred:     true,
//             startDate:        Some(start_date.get().clone()),
//         };
//         props.on_food_added.call(new_food);
//         new_food_name.set(String::new());
//         new_food_brand.set(String::new());
//         new_food_calories_per_cup.set(300);
//         selected_icon.set("beef".to_string());
//         start_date.set(chrono::Local::now().date_naive().to_string());
//         is_open.set(false);
//     };

//     let handle_food_preferred = move |food_id: String, is_preferred: bool| {
//         if is_preferred {
//             props
//                 .on_food_preferred
//                 .call((food_id.clone(), true, Some(start_date.get().clone())));
//             // Simulate setting other foods to not preferred
//             for food in props.pet_foods.iter() {
//                 if food.id != food_id && food.is_preferred {
//                     props.on_food_preferred.call((food.id.clone(), false, None));
//                 }
//             }
//         }
//     };

//     rsx!(
//         div { class: "space-y-4",
//             div { class: "flex justify-between items-center",
//                 h3 { class: "text-lg font-medium", {t!("common-food")} }
//                 button {
//                     class: "px-4 py-2 rounded-md border border-gray-300 hover:bg-gray-100 flex
// items-center gap-1",                     onclick: move |_| is_open.set(true),
//                     "➕" // Placeholder for Plus icon
//                     "{t!(\"common-add\")} {t!(\"common-food\")}"
//                 }
//             }

//             if *is_open.get() {
//                 // Simulate DialogContent
//                 div {
//                     class: "fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",
//                     div {
//                         class: "bg-white p-6 rounded-lg shadow-lg max-w-sm w-full",
//                         div { class: "mb-4",
//                             h2 { class: "text-lg font-bold", {t!("foods-add-f")}ood")} }
//                             p { class: "text-sm text-gray-500 mt-2", {t!("foods-add-f")}oodPrompt")}
// }                         }
//                         form { onsubmit: handle_submit, class: "space-y-4",
//                             label { class: "block text-sm font-medium mb-2",
// {t!("foods-food-n")}ame")} }                             input {
//                                 class: "w-full border rounded px-3 py-2",
//                                 placeholder: "Premium Dry Dog Food",
//                                 value: "{new_food_name.get()}",
//                                 oninput: move |evt| new_food_name.set(evt.value.clone()),
//                             }
//                             label { class: "block text-sm font-medium mb-2", {t!("foods-brand")}
// }                             input {
//                                 class: "w-full border rounded px-3 py-2",
//                                 placeholder: "Royal Canin",
//                                 value: "{new_food_brand.get()}",
//                                 oninput: move |evt| new_food_brand.set(evt.value.clone()),
//                                 list: "brand-options",
//                             }
//                             datalist { id: "brand-options",
//                                 for (brand, _) in brand_logos.iter() {
//                                     option { value: "{brand}" }
//                                 }
//                             }
//                             label { class: "block text-sm font-medium mb-2",
// {t!("foods-calories-p")}erCup")} }                             input {
//                                 r#type: "number",
//                                 class: "w-full border rounded px-3 py-2",
//                                 value: "{new_food_calories_per_cup.get()}",
//                                 oninput: move |evt|
// new_food_calories_per_cup.set(evt.value.parse().unwrap_or(0)),                             }

//                             div { class: "space-y-2",
//                                 label { class: "block text-sm font-medium mb-2",
// {t!("foods-select-i")}con")} }                                 div { class: "grid grid-cols-4 gap-2",
//                                     for (name, icon) in food_icons.iter() {
//                                         div {
//                                             key: "{name}",
//                                             class: "p-2 flex justify-center items-center rounded-full
// cursor-pointer {if selected_icon.get() == name { \"bg-primary text-white\" } else {
// \"text-primary-foreground\" }}",                                             onclick: move |_|
// selected_icon.set(name.to_string()),                                             "{icon}"
//                                         }
//                                     }
//                                 }
//                             }

//                             label { class: "block text-sm font-medium mb-2",
// {t!("foods-start-d")}ate")} }                             input {
//                                 r#type: "date",
//                                 class: "w-full border rounded px-3 py-2",
//                                 value: "{start_date.get()}",
//                                 oninput: move |evt| start_date.set(evt.value.clone()),
//                             }

//                             div { class: "flex justify-end gap-2 pt-4",
//                                 button {
//                                     class: "px-4 py-2 rounded-md border border-gray-300
// hover:bg-gray-100",                                     onclick: move |_| is_open.set(false),
//                                     {t!("common-cancel")}
//                                 }
//                                 button {
//                                     r#type: "submit",
//                                     class: "px-4 py-2 rounded-md bg-blue-500 text-white
// hover:bg-blue-600",                                     {t!("common-add")}
//                                 }
//                             }
//                         }
//                     }
//                 }
//             }

//             if props.pet_foods.is_empty() {
//                 div { class: "text-center p-6 border border-dashed rounded-md",
//                     p { class: "text-muted-foreground", {t!("foods-no-f")}oods")} }
//                     p { class: "text-xs text-muted-foreground", {t!("foods-add-f")}oodPrompt")} }
//                 }
//             } else {
//                 div { class: "space-y-2",
//                     for food in props.pet_foods.iter() {
//                         div {
//                             key: "{food.id}",
//                             class: "flex justify-between items-center p-3 border rounded-md {if
// food.is_preferred { \"border-primary bg-primary bg-black/10\" } else { \"\" }}",
// div { class: "flex items-center gap-3",                                 div {
//                                     class: "p-2 rounded-full {if food.is_preferred { \"bg-primary\" }
// else { \"bg-gray-300\" }}",
// "{food_icons.get(food.icon.as_str()).unwrap_or(&\"\")}"                                 }
//                                 div {
//                                     p { class: "font-medium", "{food.name}" }
//                                     div { class: "flex items-center space-x-1",
//                                         if let Some(logo_url) = &food.logo {
//                                             img { src: "{logo_url}", alt: "{food.brand}", class: "h-4
// mr-1" }                                         }
//                                         p { class: "text-xs text-gray-500", "{food.brand} •
// {food.calories_per_cup} kcal/cup" }                                     }
//                                     if food.is_preferred {
//                                         p { class: "text-xs text-pet-purple",
//                                             "{t!(\"foods-usedSince\", \"date\":
// food.startDate.as_deref().unwrap_or(\"\"))}"                                         }
//                                     }
//                                 }
//                             }
//                             div { class: "flex gap-2",
//                                 button {
//                                     class: "px-4 py-2 rounded-md {if food.is_preferred { \"bg-blue-500
// text-white\" } else { \"border border-gray-300\" }}",                                     onclick: move
// |_| handle_food_preferred(food.id.clone(), !food.is_preferred),                                     "{if
// food.is_preferred { t!(\"foods-active\") } else { t!(\"foods-use\") }}"
// }                                 button {
//                                     class: "px-2 py-1 rounded-md hover:bg-gray-100",
//                                     onclick: move |_| props.on_food_removed.call(food.id.clone()),
//                                     "✖️" // Placeholder for X icon
//                                 }
//                             }
//                         }
//                     }
//                 }
//             }

//             if let Some(history) = &props.food_history {
//                 if !history.is_empty() {
//                     div { class: "mt-6",
//                         h4 { class: "text-sm font-medium mb-2", {t!("foods-food-h")}istory")} }
//                         div { class: "space-y-1",
//                             for entry in history.iter() {
//                                 div {
//                                     key: "{entry.food_id}",
//                                     class: "text-xs border-l-2 border-gray-200 pl-3 py-1",
//                                     p { class: "font-medium", "{entry.name}" }
//                                     p { class: "text-gray-500", "{entry.brand}" }
//                                     p { class: "text-gray-400 text-[10px]",
//                                         "{entry.start_date}"
//                                         if let Some(end_date) = &entry.end_date {
//                                             " - {end_date}"
//                                         }
//                                     }
//                                 }
//                             }
//                         }
//                     }
//                 }
//             }
//         }
//     )
// }
