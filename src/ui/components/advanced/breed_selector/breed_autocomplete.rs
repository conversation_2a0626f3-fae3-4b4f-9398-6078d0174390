use std::collections::HashMap;

use dioxus::prelude::*;

use crate::{
    i18n::*,
    models::*, // Assuming dog breeds might be part of models or a separate data module
    ui::components::breed_autocomplete::{
        BreedDropdown, // Placeholder for now
        BreedInput,    // Placeholder for now
    },
};

#[derive(<PERSON>ps, PartialEq, Clone)]
pub struct BreedAutocompleteProps {
    pub value:     String,
    pub on_change: EventHandler<String>,
    #[props(default = false)]
    pub disabled:  bool,
    #[props(optional)]
    pub image_url: Option<String>,
}

#[component]
pub fn BreedAutocomplete(props: BreedAutocompleteProps) -> Element {
    // let i18n = I18n::new();

    let open = use_signal(|| false);
    let input_value = use_signal(|| props.value.clone());
    let processed_image_url = use_signal(|| None::<String>);

    // Update input value when value prop changes
    use_effect(move || {
        input_value.set(val.clone());
        async {}
    });

    // Simulate useBreedRecognition hook
    let is_recognizing = use_signal(|| false);
    let recognize_breed = move |url: String| {
        is_recognizing.set(true);
        cx.spawn(async move {
            // Simulate API call or processing
            dioxus::tokio::time::sleep(std::time::Duration::from_secs(1)).await;
            let detected_breed = "Golden Retriever".to_string(); // Mock detected breed
            input_value.set(detected_breed.clone());
            props.on_change.call(detected_breed);
            is_recognizing.set(false);
        });
    };

    // Only run breed recognition when image_url changes and hasn't been processed yet
    use_effect(
        cx,
        &(props.image_url.clone(), processed_image_url.clone()),
        |(url, processed_url)| {
            if let Some(img_url) = url {
                if processed_url.is_none() || processed_url.as_ref() != Some(img_url) {
                    recognize_breed(img_url.clone());
                    processed_image_url.set(Some(img_url.clone()));
                }
            }
            async {}
        },
    );

    let handle_input_change = move |evt: Event<FormData>| {
        let new_value = evt.value.clone();
        input_value.set(new_value.clone());
        if new_value.is_empty() {
            props.on_change.call("".to_string());
        }
    };

    let handle_breed_selection = move |breed_name: String| {
        input_value.set(breed_name.clone());
        props.on_change.call(breed_name);
        open.set(false);
    };

    let handle_clear = move |_| {
        input_value.set("".to_string());
        props.on_change.call("".to_string());
    };

    // Mock dogBreeds data
    let dog_breeds: Vec<Breed> = vec![
        Breed {
            name: "Golden Retriever".to_string(),
            id:   "golden_retriever".to_string(),
        },
        Breed {
            name: "Labrador Retriever".to_string(),
            id:   "labrador_retriever".to_string(),
        },
        Breed {
            name: "German Shepherd".to_string(),
            id:   "german_shepherd".to_string(),
        },
        Breed {
            name: "Poodle".to_string(),
            id:   "poodle".to_string(),
        },
        Breed {
            name: "Bulldog".to_string(),
            id:   "bulldog".to_string(),
        },
    ];

    let filtered_breeds: Vec<Breed> = dog_breeds
        .iter()
        .filter(|breed| {
            breed
                .name
                .to_lowercase()
                .contains(&input_value.get().to_lowercase())
            // Add i18n translation check if available
            // || t!(&format!("breeds.{}", breed.name.to_lowercase())).to_lowercase().contains(&input_value.get().to_lowercase())
        })
        .cloned()
        .collect();

    let get_localized_breed_name = |breed_name: &str| -> String {
        // Placeholder for i18n translation
        breed_name.to_string()
    };

    rsx!(
        div { class: "relative w-full",
            BreedInput {
                value: input_value.get().clone(),
                on_change: handle_input_change,
                on_click: move |_| {
                    if !props.disabled {
                        open.set(true)
                    }
                },
                on_focus: move |_| {
                    if !props.disabled {
                        open.set(true)
                    }
                },
                on_clear: handle_clear,
                disabled: props.disabled || *is_recognizing.get(),
                is_recognizing: *is_recognizing.get(),
            }

            if *open.get() && !props.disabled {
                BreedDropdown {
                    breeds: filtered_breeds,
                    selected_breed: props.value.clone(),
                    on_breed_select: handle_breed_selection,
                    get_localized_breed_name,
                }
            }
        }
    )
}
