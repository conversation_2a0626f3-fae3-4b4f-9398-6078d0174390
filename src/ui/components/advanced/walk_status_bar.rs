use crate::{
    hooks::WalkTracker,
    ui::prelude::*,
};


#[derive(<PERSON>ps, PartialEq, Eq, Clone)]
pub struct WalkStatusBarProps {
    pub tracker: Signal<WalkTracker>,
}

#[component]
pub fn WalkStatusBar(props: WalkStatusBarProps) -> Element {
    let tracker = props.tracker;

    rsx! {
        div { class: "p-4 bg-blue-100 rounded-lg",
            if tracker.read().is_walking {
                "Walking for {tracker.read().duration} seconds"
            } else {
                "Not currently walking."
            }
        }
    }
}
