// use dioxus::prelude::*;

// use crate::i18n::*;

// #[derive(<PERSON><PERSON>, PartialEq, Clone)]
// pub struct SizeSelectorProps {
//     pub selected_size:    Option<String>,
//     pub on_size_selected: EventHandler<String>,
// }

// #[component]
// pub fn SizeSelector(props: SizeSelectorProps) -> Element {
//     rsx! {
//         div {
//             label { class: "block text-sm font-medium mb-2",
//                 {t!("onboarding-size")}
//                 span { class: "text-red-500", " *" }
//             }
//             div { class: "flex justify-between",
//                 button {
//                     class: "flex flex-col items-center gap-1 w-[32%] h-24 {if props.selected_size ==
// Some(\"small\".to_string()) { \"bg-pet-green text-black\" } else { \"\" }}",                     onclick:
// move |_| props.on_size_selected.call("small".to_string()),                     // Placeholder for Dog
// icon (small)                     div { class: "h-6 w-6", "🐕" }
//                     span { class: "text-sm", {t!("onboarding-small")} }
//                     span { class: "text-xs text-gray-500", {t!("onboarding-small-w")}eight")} }
//                 }

//                 button {
//                     class: "flex flex-col items-center gap-1 w-[32%] h-24 {if props.selected_size ==
// Some(\"medium\".to_string()) { \"bg-pet-yellow text-black\" } else { \"\" }}",
// onclick: move |_| props.on_size_selected.call("medium".to_string()),                     // Placeholder
// for Dog icon (medium)                     div { class: "h-8 w-8", "🐕" }
//                     span { class: "text-sm", {t!("onboarding-medium")} }
//                     span { class: "text-xs text-gray-500", {t!("onboarding-medium-w")}eight")} }
//                 }

//                 button {
//                     class: "flex flex-col items-center gap-1 w-[32%] h-24 {if props.selected_size ==
// Some(\"large\".to_string()) { \"bg-pet-orange text-black\" } else { \"\" }}",
// onclick: move |_| props.on_size_selected.call("large".to_string()),                     // Placeholder
// for Dog icon (large)                     div { class: "h-10 w-10", "🐕" }
//                     span { class: "text-sm", {t!("onboarding-large")} }
//                     span { class: "text-xs text-gray-500", {t!("onboarding-large-w")}eight")} }
//                 }
//             }
//         }
//     }
// }
