use dioxus_free_icons::icons::ld_icons::{
    LdAct<PERSON>,
    LdBone,
    LdCalendar,
    LdCircle<PERSON><PERSON>ck,
    Ld<PERSON><PERSON><PERSON><PERSON>,
    LdGauge,
    LdHeart,
    LdHourglass,
    LdTrendingUp,
    LdWeight,
};

use crate::{
    hooks::use_walk_tracker,
    prelude::*,
    ui::components::advanced::{
        MetricCard,
        WalkStatusBar,
    },
};


mod daily_diet_card;
use daily_diet_card::DailyDietCard;
mod dog_recommendation_card;
use dog_recommendation_card::RecommendationCard;
mod treat_tracker;
use treat_tracker::TreatTracker;


#[component]
pub fn Home() -> Element {
    let state = use_state_by_event(AppEvent::DogUpdated);
    let state_snapshot = state.read();
    let dogs: &Vec<Dog> = state_snapshot.dogs.as_ref();
    if dogs.is_empty() {
        return rsx! { "No dog selected" };
    }
    let dog = state_snapshot.get_selected_dog().unwrap();

    let handle_add_treat = move || {
        // let treats_today = dog.treats_today.unwrap_or(0) + 1;
        // let diet_adjustment = (treats_today * 5).min(30); // Cap at 30%
        // dog.treats_today = Some(treats_today);
        // dog.diet_adjustment = Some(diet_adjustment as i32);
    };

    let handle_remove_treat = move || {
        // if dog.treats_today.unwrap_or(0) > 0 {
        //     let treats_today = dog.treats_today.unwrap_or(0) - 1;
        //     let diet_adjustment = (treats_today * 5).max(0);
        //     dog.treats_today = Some(treats_today);
        //     dog.diet_adjustment = Some(diet_adjustment as i32);
        // }
    };

    let get_diet_impact_text = move || -> String {
        // let adjustment = dog.diet_adjustment.unwrap_or(0);
        // if adjustment == 0 {
        t!("diet.none")
        // } else {
        //     format!("{:+}% {}", adjustment, t!("diet.calories"))
        // }
    };

    let calorie_progress = dog.calorie_progress.unwrap_or(0);
    let daily_calorie_goal = dog.daily_calorie_goal.unwrap_or(2000);

    rsx! {
      Page {
        PageHeaderProfile { dog: dog.clone(), dogs: dogs.clone() }

        Section { class: "px-0", colored: true,
          // * Health Metrics
          Section {
            title: t!("home-health-metrics"),
            icon: IconRef::new(&LdActivity),
            div { class: "grid grid-cols-2 gap-4",
              MetricCard {
                icon: IconRef::new(&LdGauge),
                title: "BCS",
                value: dog.body_fit_state.unwrap_or(7),
                unit: "",
                color: "teal",
              }
              Link { to: "/activities/history",
                MetricCard {
                  icon: IconRef::new(&LdTrendingUp),
                  title: "BMI",
                  value: dog.daily_activity_goal.unwrap_or(0),
                  unit: "",
                  color: "blue",
                }
              }
              Link { to: "/weight-history",
                MetricCard {
                  icon: IconRef::new(&LdWeight),
                  title: "Weight",
                  value: dog.weight.unwrap_or(DogWeight(0.0)),
                  unit: "kg",
                  color: "purple",
                }
              }
              MetricCard {
                icon: IconRef::new(&LdHeart),
                title: "Health Score",
                value: "85",
                unit: "%",
                color: "pink",
              }
            }
          }

          // * Today's Activity
          Section {
            title: t!("home-todays-activity"),
            icon: IconRef::new(&LdHourglass),

            TodayActivitySummary {}
          }

          // * Today's Diet Card
          Section {
            title: t!("diet-todays-diet"),
            icon: IconRef::new(&LdBone),
            DailyDietCard {
              daily_calories: daily_calorie_goal,
              preferred_foods: dog.preferred_foods.clone(),
              diet_adjustment: 0, //dog.diet_adjustment.unwrap_or(0),
            }
            TreatTracker {
              treats_count: 0, // dog.treats_today.unwrap_or(0),
              on_treat_added: handle_add_treat,
              on_treat_removed: handle_remove_treat,
              diet_impact: get_diet_impact_text(),
            }
          }

          // * Nutrition Summary
          Section {
            title: t!("home-nutrition-summary"),
            icon: IconRef::new(&LdEggFried),
            ProgressBar {
              title: t!("home-daily-calories"),
              current_value: calorie_progress,
              total_value: daily_calorie_goal,
              unit: "kcal".to_string(),
            }

            div { class: "grid grid-cols-3 gap-2 mt-4",
              Card { class: "items-center bg-background-dark/15 border-background-dark/25",
                p { class: "text-sm text-gray-700", {t!("home-protein")} }
                p { class: "font-bold text-black", "35%" }
              }
              Card { class: "items-center bg-background-dark/15 border-background-dark/25",
                p { class: "text-sm text-gray-700", {t!("home-fats")} }
                p { class: "font-bold text-black", "25%" }
              }
              Card { class: "items-center bg-background-dark/15 border-background-dark/25",
                p { class: "text-sm text-gray-700", {t!("home-carbs")} }
                p { class: "font-bold text-black", "40%" }
              }
            }
          }
        }

        // * Recommendation Card (if any)
        // if current_recommendation().is_some() {
        RecommendationCard {}
            // }
      }
    }
}


#[component]
pub fn TodayActivitySummary() -> Element {
    // info!("TodayActivitySummary()");
    let state = use_state_by_event(AppEvent::ActivityStateUpdated);
    let state_snapshot = state.read();
    let dog = state_snapshot.get_selected_dog().unwrap();

    let activity_progress = dog.get_total_activity_duration_today().num_minutes() as u32;
    let daily_activity_goal = dog.daily_activity_goal.unwrap_or(30);

    rsx! {
      ProgressBar {
        current_value: activity_progress,
        total_value: daily_activity_goal,
        unit: "min".to_string(),
      }
    }
}
