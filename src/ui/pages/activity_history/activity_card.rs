use crate::ui::prelude::*;


#[component]
pub fn ActivityCard(
    calories: u32,
    class: String,
    color: String,
    duration: String,
    icon: String,
    time: String,
    title: String,
) -> Element {
    rsx! {
      div {
        class: "border-0 shadow-xs hover:shadow-md transition-all",
        class: "{class}",
        div { class: "p-4 flex items-center space-x-3",
          div { class: "p-3 rounded-full flex items-center justify-center bg-pet-lightBlue",
            {icon} // Replace with actual icon
          }
          div { class: "flex-1",
            div { class: "flex justify-between",
              p { class: "font-medium", {title} }
              p { class: "text-sm text-gray-500", {time} }
            }
            div { class: "flex items-center mt-1 text-sm text-gray-500",
              span { class: "mr-3", {duration} }
              span { "{calories} kcal" }
            }
          }
        }
      }
    }
}
