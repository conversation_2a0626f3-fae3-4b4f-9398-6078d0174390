use dioxus::prelude::*;

use crate::{
    state::AppState,
    // ui::components::*,
};

mod activity_card;
use activity_card::ActivityCard;

#[component]
pub fn ActivityHistoryPage() -> Element {
    rsx!(
        div {
            "Activity History Page"
            ActivityCard {
                icon: "Activity".to_string(),
                title: "Walk".to_string(),
                time: "10:00 AM".to_string(),
                duration: "30 min".to_string(),
                calories: 150,
                color: "bg-pet-lightBlue".to_string(),
                class: String::new(),
            }
        }
    )
}
