use crate::ui::prelude::*;


#[component]
pub fn NotFound(path: String) -> Element {
    rsx! {
      div { class: "flex flex-col items-center justify-center min-h-screen text-center",
        h1 { class: "text-4xl font-bold mb-4", "404 - Page Not Found" }
        p { "The page at `/{path}` was not found." }
        Link {
          to: "/",
          class: "mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600",
          "Go to Home"
        }
      }
    }
}
