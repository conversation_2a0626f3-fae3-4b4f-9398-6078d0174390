use crate::ui::prelude::*;


#[derive(<PERSON><PERSON>, PartialEq, <PERSON>lone)]
pub struct EmailVerificationStatusProps {
    pub user: UserProfile,
}

#[component]
pub fn EmailVerificationStatus(props: EmailVerificationStatusProps) -> Element {
    if props.user.email.is_none() {
        return rsx! { "No email provided" };
    }

    // Simulate provider check
    let is_google_provider = props.user.provider.as_deref() == Some("google");
    let is_apple_provider = props.user.provider.as_deref() == Some("apple");
    let is_email_confirmed = props.user.email_confirmed_at.is_some();

    rsx! {
      if is_google_provider {
        div { class: "flex items-center gap-1 text-xs text-green-600",
          // Placeholder for Google icon
          div { class: "h-4 w-4", "G" }
          "Verified"
        }
      } else if is_apple_provider {
        div { class: "flex items-center gap-1 text-xs text-green-600",
          // Placeholder for Apple icon
          div { class: "h-4 w-4", "🍎" }
          "Verified"
        }
      } else if is_email_confirmed {
        div { class: "flex items-center gap-1 text-xs text-green-600",
          // Placeholder for Check icon
          div { class: "h-3 w-3", "✔️" }
          "Verified"
        }
      } else {
        div { class: "flex items-center gap-1 text-xs text-amber-600",
          // Placeholder for Mail icon
          div { class: "h-3 w-3", "📧" }
          "Unverified - check your email"
        }
      }
    }
}
