use crate::prelude::*;


#[component]
pub fn NotificationsSection() -> Element {
    let mut state = use_state_by_event(AppEvent::NotificationsToggle);

    rsx! {
      Section {
        title: "Notifications",
        description: "Manage your notification settings.",
        Content {
          Row {
            span { "Enable Notifications" }
            Switch {
              checked: state.read().user.notifications_enabled,
              on_change: move |v| {
                  state
                      .apply(AppAction::NotificationsToggle {
                          enabled: v,
                      });
              },
            }
          }
        }
      }
    }
}
