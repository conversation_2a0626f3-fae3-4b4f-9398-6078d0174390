use crate::ui::prelude::*;


#[derive(<PERSON><PERSON>, PartialEq, <PERSON>lone)]
pub struct PetInfoFormProps {
    pub pet_name:  Signal<String>,
    pub pet_size:  Signal<String>,
    #[props(default)]
    pub on_back:   EventHandler<()>,
    #[props(default)]
    pub on_finish: EventHandler<()>,
}

#[component]
pub fn PetInfoForm(props: PetInfoFormProps) -> Element {
    rsx! {
      div { class: "space-y-4",
        div {
          Label { r#for: "pet_name", "Pet's Name" }
          Input {
            id: "pet_name",
            value: props.pet_name,
            placeholder: "Your Pet's Name",
          }
        }
        div {
          Label { "Size" }
          Select {
            SelectTrigger {
              SelectValue { placeholder: Some(props.pet_size.read().clone()) }
            }
            SelectContent {
              // In a real app, onchange would be handled here
              SelectItem { value: "small".to_string(), "Small" }
              SelectItem { value: "medium".to_string(), "Medium" }
              SelectItem { value: "large".to_string(), "Large" }
            }
          }
        }
        div { class: "flex justify-between",
          Button {
            variant: ButtonVariant::Outline,
            onclick: move |_| props.on_back.call(()),
            "Back"
          }
          Button { onclick: move |_| props.on_finish.call(()), "Finish" }
        }
      }
    }
}
