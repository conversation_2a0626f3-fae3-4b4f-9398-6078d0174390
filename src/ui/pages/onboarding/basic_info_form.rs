use crate::ui::prelude::*;


#[derive(Props, PartialEq, Clone)]
pub struct BasicInfoFormProps {
    pub name:    Signal<String>,
    pub email:   Signal<String>,
    #[props(default)]
    pub on_next: EventHandler<()>,
}

#[component]
pub fn BasicInfoForm(props: BasicInfoFormProps) -> Element {
    rsx! {
      div { class: "space-y-4",
        div {
          Label { r#for: "name", "Name" }
          Input {
            id: "name",
            value: props.name,
            placeholder: "Your Name",
          }
        }
        div {
          Label { r#for: "email", "Email" }
          Input {
            id: "email",
            r#type: "email",
            value: props.email,
            placeholder: "<EMAIL>",
          }
        }
        Button { onclick: move |_| props.on_next.call(()), "Next" }
      }
    }
}
