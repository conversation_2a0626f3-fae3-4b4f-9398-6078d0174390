use chrono::{
    DateTime,
    Utc,
};

use crate::ui::prelude::*;

#[component]
pub fn AiAssistantPage() -> Element {
    let mut messages = use_signal(Vec::<ChatMessage>::new);
    let mut current_input = use_signal(String::new);
    let mut is_loading = use_signal(|| false);

    // Initialize with a welcome message
    use_effect(move || {
        if messages.read().is_empty() {
            messages.write().push(ChatMessage {
                id:           1,
                content:      "Hello! I'm your AI assistant. I can help you with nutrition \
                               recommendations, analyze food labels, and answer questions about your \
                               dog's health and fitness."
                    .to_string(),
                is_user:      false,
                message_type: MessageType::Text,
                timestamp:    Utc::now(),
            });
        }
    });

    let send_message = move |_| {
        let input = current_input.read().clone();
        if input.trim().is_empty() {
            return;
        }

        // Add user message
        let user_message = ChatMessage {
            id:           messages.read().len() + 1,
            content:      input.clone(),
            is_user:      true,
            message_type: MessageType::Text,
            timestamp:    Utc::now(),
        };
        messages.write().push(user_message);

        // Clear input
        current_input.set(String::new());

        // Set loading state
        is_loading.set(true);

        // TODO: Implement actual Gemini API call
        // For now, add a placeholder response
        spawn(async move {
            // Simulate API delay
            gloo_timers::future::TimeoutFuture::new(1000).await;

            let response = ChatMessage {
                id:           messages.read().len() + 1,
                content:      "I'm here to help! This is a placeholder response. Soon I'll be powered by \
                               Gemini AI to provide personalized recommendations for your dog's health \
                               and nutrition."
                    .to_string(),
                is_user:      false,
                message_type: MessageType::Text,
                timestamp:    Utc::now(),
            };

            messages.write().push(response);
            is_loading.set(false);
        });
    };

    let handle_quick_action = move |action: &'static str| {
        current_input.set(action.to_string());
        send_message(());
    };

    rsx! {
        div { class: "flex flex-col h-full bg-background",
            // Header
            div { class: "flex items-center justify-between p-4 border-b border-border bg-card",
                h1 { class: "text-xl font-semibold text-foreground", "AI Assistant" }
                        // TODO: Add dog selection dropdown here
            }

            // Chat messages
            div { class: "flex-1 overflow-y-auto p-4 space-y-4",
                for message in messages.read().iter() {
                    ChatMessageComponent { message: message.clone() }
                }
                if *is_loading.read() {
                    div { class: "flex justify-start",
                        div { class: "bg-muted text-muted-foreground px-4 py-2 rounded-lg max-w-xs",
                            div { class: "flex items-center space-x-2",
                                div { class: "w-2 h-2 bg-current rounded-full animate-pulse" }
                                div { class: "w-2 h-2 bg-current rounded-full animate-pulse animation-delay-100" }
                                div { class: "w-2 h-2 bg-current rounded-full animate-pulse animation-delay-200" }
                            }
                        }
                    }
                }
            }

            // Quick actions
            div { class: "p-4 border-t border-border bg-card",
                div { class: "mb-4",
                    h3 { class: "text-sm font-medium text-muted-foreground mb-2", "Quick Actions" }
                    div { class: "flex flex-wrap gap-2",
                        QuickActionButton {
                            text: "Analyze food label",
                            onclick: move |_| handle_quick_action("I need help analyzing a food label for my dog"),
                        }
                        QuickActionButton {
                            text: "Nutrition advice",
                            onclick: move |_| handle_quick_action("Can you give me nutrition advice for my dog?"),
                        }
                        QuickActionButton {
                            text: "Exercise recommendations",
                            onclick: move |_| handle_quick_action("What exercises do you recommend for my dog?"),
                        }
                        QuickActionButton {
                            text: "Health tips",
                            onclick: move |_| handle_quick_action("Can you give me general health tips for my dog?"),
                        }
                    }
                }

                // Message input
                div { class: "flex space-x-2",
                    input {
                        r#type: "text",
                        class: "flex-1 px-3 py-2 border border-input bg-background rounded-md text-sm placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
                        placeholder: "Ask me anything about your dog's health...",
                        value: "{current_input}",
                        oninput: move |evt| current_input.set(evt.value()),
                        onkeypress: move |evt| {
                            if evt.key() == Key::Enter {
                                send_message(());
                            }
                        },
                    }
                    Button {
                        variant: ButtonVariant::Default,
                        size: Size::Md,
                        onclick: send_message,
                        disabled: current_input.read().trim().is_empty() || *is_loading.read(),
                        "Send"
                    }
                }
            }
        }
    }
}

#[component]
fn ChatMessageComponent(message: ChatMessage) -> Element {
    let message_class = if message.is_user {
        "flex justify-end"
    } else {
        "flex justify-start"
    };

    let bubble_class = if message.is_user {
        "bg-primary text-primary-foreground px-4 py-2 rounded-lg max-w-xs lg:max-w-md"
    } else {
        "bg-muted text-muted-foreground px-4 py-2 rounded-lg max-w-xs lg:max-w-md"
    };

    rsx! {
        div { class: "{message_class}",
            div { class: "{bubble_class}",
                match message.message_type {
                    MessageType::Text => rsx! {
                        div {
                            p { class: "text-sm", "{message.content}" }
                            p { class: "text-xs opacity-70 mt-1", {format!("{}", message.timestamp.format("%H:%M"))} }
                        }
                    },
                    MessageType::Image => rsx! {
                        div {
                            // TODO: Implement image message display
                            p { class: "text-sm italic", "Image analysis feature coming soon..." }
                            p { class: "text-xs opacity-70 mt-1", {format!("{}", message.timestamp.format("%H:%M"))} }
                        }
                    },
                    MessageType::FoodAnalysis => rsx! {
                        div {
                            // TODO: Implement food analysis display with structured data
                            p { class: "text-sm", "{message.content}" }
                            p { class: "text-xs opacity-70 mt-1", {format!("{}", message.timestamp.format("%H:%M"))} }
                        }
                    },
                }
            }
        }
    }
}

#[component]
fn QuickActionButton(text: &'static str, onclick: EventHandler<MouseEvent>) -> Element {
    rsx! {
        Button {
            variant: ButtonVariant::Outline,
            size: Size::Sm,
            onclick: move |evt| onclick.call(evt),
            "{text}"
        }
    }
}

// Data structures
#[derive(Clone, PartialEq)]
pub struct ChatMessage {
    pub id:           usize,
    pub content:      String,
    pub is_user:      bool,
    pub message_type: MessageType,
    pub timestamp:    DateTime<Utc>,
}

#[derive(Clone, PartialEq)]
pub enum MessageType {
    Text,
    Image,
    FoodAnalysis,
}

// TODO: Implement Gemini API integration
pub async fn call_gemini_api(_message: &str, _message_type: MessageType) -> Result<String, String> {
    // Placeholder for Gemini API integration
    // This will be implemented when we add the actual AI functionality
    Ok("This is a placeholder response from the AI assistant.".to_string())
}
