use chrono::prelude::*;
use dioxus_free_icons::icons::ld_icons::LdCamera;

use crate::prelude::*;

mod editable_field;
use editable_field::EditableField;
mod delete_dog_dialog;
use delete_dog_dialog::DeleteDogDialog;

#[component]
pub fn DogProfile() -> Element {
    debug!("DogProfile()");
    let mut state = use_state_by_event(AppEvent::DogUpdated);
    let state_snapshot = state.read();
    let dogs = &state_snapshot.dogs;
    if dogs.is_empty() {
        // let navigator = use_navigator();
        return rsx! { "No dog selected" };
    }
    let dog = state_snapshot.get_selected_dog().unwrap();
    macro_rules! dog_mut {
        () => {
            state.write().get_selected_dog_mut().unwrap()
        };
    }


    let mut show_delete_dialog = use_signal(|| false);

    let handle_delete = move |()| {
        // if let Some(mut state) = state.write() {
        //     state.pets.retain(|p| p.id != pet_id);
        //     if state.pets.is_empty() {
        //         navigator.push("/");
        //     }
        // }
        show_delete_dialog.set(false);
    };

    let handle_name_change = move |new_name: String| {
        dog_mut!().name = new_name;
    };
    let handle_birthday_change = move |new_birthday: Option<DateTime<Utc>>| {
        debug!("on_change(): birthday {}", new_birthday.unwrap_or_default());
        dog_mut!().birthday = new_birthday.map(|dt| dt.date_naive());
    };
    let handle_breed_change = move |new_breed: String| {
        debug!("on_change() breed: {new_breed}");
        dog_mut!().breed_id = if new_breed.is_empty() { None } else { Some(new_breed) };
    };
    let handle_weight_change = move |new_weight: DogWeight| {
        dog_mut!().current_weight = Some(*new_weight);
    };
    let handle_size_change = move |new_size: String| {
        debug!("on_change() size: {new_size}");
        if let Ok(size) = DogSize::try_from(new_size) {
            dog_mut!().size = size;
        }
    };

    rsx! {
      Page {
        PageHeaderProfile { dog: dog.clone(), dogs: dogs.clone() }

        Section { colored: true,
          // * Photo
          div {
            class: "rounded-3xl overflow-clip col items-center text-gray-300 hover:text-gray-200 mb-14",
            class: if dog.image().is_some() { "relative shadow-xl" } else { "shadow-glow-sm border-2 border-dashed border-secondary p-6 bg-secondary/5 text-secondary/80 hover:text-secondary m-6 mb-4" },
            style: if dog.image().is_some() { "aspect-ratio: 4/3" },
            if let Some(image_url) = dog.image() {
              img {
                src: "{image_url}",
                alt: "{dog.name}",
                class: "absolute w-full h-full object-cover",
              }
            }
            div { class: "mt-2 flex text-sm",
              label {
                r#for: "file-upload",
                class: "relative cursor-pointer rounded-md font-medium",
                Icon {
                  size: Size::Xl,
                  icon: IconRef::new(&LdCamera),
                }
                if dog.image().is_none() {
                  span { class: "text-gray-800", {t!("dog-profile-upload-photo")} }
                }
                input {
                  id: "file-upload",
                  name: "file-upload",
                  r#type: "file",
                  class: "sr-only",
                  accept: "image/*",
                                // onChange: handlePhotoUpload,
                }
              }
            }
          }

          div { class: "space-y-5 my-4 text-sm",
            EditableField {
              label: t!("dog-profile-name"),
              value: dog.name.clone(),
              // on_change: move |new_name: String| handle_name_change(*dog.clone(), new_name),
              on_change: handle_name_change,
            }
            // EditableField {
            //   label: t!("dog-profile-birthday"),
            //   value: dog.birthday.map_or_else(|| "Not set".to_string(), |d| d.to_string()),
            //   on_change: handle_birthday_change,
            // }
            EditableField {
              label: t!("dog-profile-breed"),
              value: dog.breed(),
              on_change: handle_breed_change,
            }
            EditableField {
              label: t!("dog-profile-weight"),
              value: dog.weight(),
              on_change: handle_weight_change,
            }
            EditableField {
              label: t!("dog-profile-size"),
              value: dog.size,
              on_change: handle_size_change,
            }
          }

          Button {
            variant: ButtonVariant::Destructive,
            class: "w-full mt-16",
            onclick: move |_| show_delete_dialog.set(true),
            {t!("dog-profile-delete-dog")}
          }
        }

        DeleteDogDialog {
          is_open: show_delete_dialog(),
          on_close: move || show_delete_dialog.set(false),
          on_delete: handle_delete,
          dog_name: dog.name.clone(),
        }
        ""

        // FoodManagement {
        //   pet_foods: dog.preferred_foods.clone(),
        //   food_history: dog.food_history.clone(),
        //   on_food_added: handle_add_pet_food,
        //   on_food_removed: handle_remove_pet_food,
        //   on_food_preferred: handle_food_preferred,
        // }
        ""
      }
    }
}
