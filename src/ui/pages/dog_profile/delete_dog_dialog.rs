use dioxus::prelude::*;

use crate::i18n::t;

#[derive(<PERSON><PERSON>, PartialEq, <PERSON>lone)]
pub struct DeleteDogDialogProps {
    pub is_open:   bool,
    pub on_close:  EventHandler<()>,
    #[props(optional)]
    pub on_delete: Option<EventHandler<()>>,
    pub dog_name:  String,
    #[props(optional)]
    pub dog_id:    Option<String>,
}

#[component]
pub fn DeleteDogDialog(props: DeleteDogDialogProps) -> Element {
    if !props.is_open {
        return rsx! {};
    }
    // * Animation - https://dioxuslabs.github.io/components/component/?name=dialog
    rsx! {
      div { class: "fixed inset-0 flex items-center justify-center z-50 bg-black/50",
        div { class: "bg-white p-6 rounded-lg shadow-lg max-w-sm w-full",
          div { class: "mb-4",
            h2 { class: "text-lg font-bold", {t!("delete-dog-dialog-title")} }
            p { class: "text-sm text-gray-500 mt-2",
              // {t!("delete-dog-dialog-description", "dogName" : props.dog_name.clone())}
              {t!("delete-dog-dialog-description")}
            }
          }
          div { class: "flex justify-end space-x-2",
            button {
              class: "px-4 py-2 rounded-md border border-gray-300 hover:bg-gray-100",
              onclick: move |_| props.on_close.call(()),
              {t!("common-cancel")}
            }
            button {
              class: "px-4 py-2 rounded-md bg-red-500 text-white hover:bg-red-600",
              onclick: move |_| {
                  if let Some(on_delete) = &props.on_delete {
                      on_delete.call(());
                  }
              },
              {t!("common-delete")}
            }
          }
        }
      }
    }
}
