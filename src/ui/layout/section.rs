use crate::ui::prelude::*;


#[derive(<PERSON><PERSON>, <PERSON><PERSON>, PartialEq)]
pub struct SectionProps {
    #[props(default = false)]
    pub colored:     bool,
    #[props(default)]
    pub class:       String,
    #[props(default)]
    pub title:       String,
    #[props(default)]
    pub description: String,
    #[props(default)]
    pub icon:        IconRef,
    #[props(default)]
    pub children:    Element,
}

/// # Section
///
/// A section is a container for content. It can be colored or not.
///
/// ## Example
///
/// ```rust
/// rsx! {
///     Section { class: "mb-8",
///         title: "Title",
///         description: "This is a description",
///         colored: true,
///         // Content
///         h3 { class: "text-lg font-bold mb-4", "Some content" }
///     }
/// }
/// ```
///
/// ## Styling
///
/// - `section`: `mb-4 pt-4 pb-1 page-content`
/// - `section-colored`: `section rounded-3xl shadow-glow-sm bg-background`
#[component]
pub fn Section(props: SectionProps) -> Element {
    rsx! {
      div { class: if props.colored { "section-colored {props.class}" } else { "section {props.class}" },
        if !props.title.is_empty() || !props.description.is_empty() {
          Header {
            if !props.title.is_empty() {
              Title { icon: props.icon, {props.title} }
            }
            if !props.description.is_empty() {
              Description { {props.description} }
            }
          }
        }
        {props.children}
      }
    }
}


#[derive(Props, Clone, PartialEq)]
pub struct TitleProps {
    #[props(default)]
    pub children: Element,
    #[props(default)]
    pub class:    String,
    #[props(optional)]
    pub icon:     IconRef,
}

#[component]
pub fn Title(props: TitleProps) -> Element {
    rsx! {
      if props.icon.is_some() {
        Row { class: "justify-start",
          h2 { class: "title {props.class} mr-2", {props.children} }
                // Icon {
        //   class: "text-black/50",
        //   size: Size::Xs,
        //   icon: props.icon,
        // }
        }
      } else {
        h2 { class: "title {props.class}", {props.children} }
      }
    }
}
