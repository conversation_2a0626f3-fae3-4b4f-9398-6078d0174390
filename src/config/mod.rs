use std::env;

#[cfg(feature = "dotenvy")]
use dotenvy::dotenv;
use serde::{
    Deserialize,
    Serialize,
};

/// Application configuration containing Supabase and other service credentials
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Config {
    pub supabase_url:      String,
    pub supabase_anon_key: String,
    pub gemini_api_key:    Option<String>,
}

impl Config {
    /// Create a new Config from environment variables
    pub fn from_env() -> Self {
        #[cfg(feature = "dotenvy")]
        {
            dotenv().ok(); // Load .env file if it exists
        }

        Self {
            supabase_url:      env::var("SUPABASE_URL")
                .unwrap_or_else(|_| "https://your-project.supabase.co".to_string()),
            supabase_anon_key: env::var("SUPABASE_ANON_KEY")
                .unwrap_or_else(|_| "your-anon-key".to_string()),
            gemini_api_key:    env::var("GEMINI_API_KEY").ok(),
        }
    }

    /// Create a default config for development/testing
    pub fn default_dev() -> Self {
        Self {
            supabase_url:      "https://localhost:54321".to_string(),
            supabase_anon_key: "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.\
                                eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.\
                                CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0"
                .to_string(),
            gemini_api_key:    None,
        }
    }
}

impl Default for Config {
    fn default() -> Self { Self::from_env() }
}
