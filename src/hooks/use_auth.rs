use chrono::Utc;
use dioxus::{
    prelude::*,
    signals::Signal,
};

use crate::{
    config::Config,
    integrations::supabase::client::SupabaseClient,
    models::{
        Theme,
        UserProfile,
    },
};

pub fn use_auth() -> Signal<Option<UserProfile>> {
    let config = Config::from_env();
    let client = SupabaseClient::new(config.clone());

    // This is still a mock, but now it's structured to call the client
    let user_profile = use_signal(|| None::<UserProfile>);

    // Example of how you might use the client in an async task
    // use_future(move || async move {
    //     let req = AuthRequest { email: "<EMAIL>", password: "password" };
    //     if let Ok(res) = client.sign_in(&req).await {
    //         // In a real app, you'd decode the token to get user info
    //         user_profile.set(Some(UserProfile {
    //             email: Some("<EMAIL>".to_string()),
    //             // ... fill other fields
    //         }));
    //     }
    // });

    Signal::new(Some(UserProfile {
        id:                    "test_user_id".to_string(),
        email:                 Some("<EMAIL>".to_string()),
        provider:              Some("email".to_string()),
        notifications_enabled: true,
        unit_system:           "metric".to_string(),
        preferred_language:    "en".to_string(),
        theme:                 Theme::Light,
        onboarding_completed:  false,
        created_at:            Some(Utc::now()),
        updated_at:            Some(Utc::now()),
    }))
}
