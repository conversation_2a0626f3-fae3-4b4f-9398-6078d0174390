use crate::{
    prelude::*,
    state::{
        AppAction,
        use_state,
    },
    ui::routes::Route,
};

pub fn use_onboarding() -> Signal<OnboardingState> {
    let mut state = use_signal(OnboardingState::default);
    let mut app_state = use_state();

    // TODO: Implement the logic to save the user and dog profiles
    // and navigate to the home page.

    state
}

#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>)]
pub struct OnboardingState {
    pub user_name:  String,
    pub user_email: String,
    pub dog_name:   String,
    pub dog_size:   String,
}
