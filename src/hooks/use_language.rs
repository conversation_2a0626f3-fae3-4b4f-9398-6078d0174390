// TODO: Check if this is still needed

// use std::collections::HashMap;

// use dioxus::prelude::*;

// use crate::{
//     i18n::*,
//     utils::local_storage_helpers::{
//         get_item,
//         save_item,
//     },
// };

// #[derive(Clone, PartialEq)]
// pub struct LanguageContextType {
//     pub language:     String,
//     pub set_language: EventHandler<String>,
//     pub t:            Translations,
//     pub format:       EventHandler<(String, Option<HashMap<String, String>>)>,
// }

// #[derive(Props, PartialEq, Clone)]
// pub struct LanguageProviderProps {
//     pub children: Element,
// }

// #[component]
// pub fn LanguageProvider(props: LanguageProviderProps) -> Element {
//     let language = use_signal(|| get_item("language").unwrap_or_else(|| "en".to_string()));

//     let translations_map = translations(); // Get the HashMap of translations

//     let t = use_memo(move || {
//         translations_map
//             .get(lang.as_str())
//             .cloned()
//             .unwrap_or_default()
//     });

//     use_effect(move || {
//         save_item("language", lang);
//         async {}
//     });

//     let set_language = move |new_lang: String| {
//         language.set(new_lang);
//     };

//     let format = move |(key, params): (String, Option<HashMap<String, String>>)| {
//         let keys: Vec<&str> = key.split('.').collect();
//         let mut value: Option<String> = None;

//         // This part needs to dynamically traverse the `t` (Translations) structure.
//         // Since `Translations` is a flat struct in Rust, we'll simulate the dot notation
//         // by having a large match statement or a helper function if `t` were nested.
//         // For now, we'll assume `t` has direct fields for top-level keys like "common", "diet", etc.
//         // and then we'd access sub-fields. This is a simplification.

//         // A more robust solution would involve a macro or a more complex data structure for
// `Translations`.         // For demonstration, let's assume `t` has a method to get a string by key.
//         // This is a placeholder for actual nested translation lookup.
//         if let Some(translation_value) = t.get_string(&key) {
//             value = Some(translation_value);
//         }


//         let mut formatted_string = value.unwrap_or(key.clone());

//         if let Some(params_map) = params {
//             for (param_key, param_value) in params_map {
//                 formatted_string = formatted_string.replace(&format!("{{{}}}", param_key), &param_value);
//             }
//         }
//         formatted_string
//     };

//     let language_context = LanguageContextType {
//         language:     language.get().clone(),
//         set_language: set_language.into(),
//         t:            t.get().clone(),
//         format:       format.into(),
//     };

//     use_context_provider(|| language_context);

//     rsx! {
//         {props.children.clone()}
//     }
// }

// pub fn use_language(cx: Scope) -> LanguageContextType {
//     use_context::<LanguageContextType>(cx).expect("use_language must be used within a LanguageProvider")
// }
