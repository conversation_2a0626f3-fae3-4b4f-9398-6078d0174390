use chrono::NaiveDate;
use dioxus::{
    prelude::*,
    signals::Signal,
};

use crate::models::WeightLog;

// Mock implementation of use_weight_log_data hook
pub fn use_weight_log_data() -> Signal<Vec<WeightLog>> {
    // In a real app, you would fetch this from a database or local storage.
    // For now, we return a static list of weight logs.
    Signal::new(vec![
        WeightLog {
            id:         "log1".to_string(),
            dog_id:     "1".to_string(),
            weight_kg:  15.5,
            date:       NaiveDate::from_ymd_opt(2024, 7, 1).unwrap(),
            notes:      None,
            created_at: None,
        },
        WeightLog {
            id:         "log2".to_string(),
            dog_id:     "1".to_string(),
            weight_kg:  15.8,
            date:       NaiveDate::from_ymd_opt(2024, 7, 8).unwrap(),
            notes:      Some("Looking good!".to_string()),
            created_at: None,
        },
    ])
}
