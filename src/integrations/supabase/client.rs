use std::collections::HashMap;

use postgrest::Postgrest;
use serde::{
    Deserialize,
    Serialize,
};

use super::auth::{
    AuthError,
    SupabaseAuth,
};
use crate::config::Config;

/// Main Supabase client that handles both database and authentication operations
pub struct SupabaseClient {
    db_client:   Postgrest,
    auth_client: SupabaseAuth,
    config:      Config,
}

#[derive(Debug, thiserror::Error)]
pub enum SupabaseError {
    #[error("Authentication error: {0}")]
    Auth(#[from] AuthError),
    #[error("Database error: {0}")]
    Database(String),
    #[error("Network error: {0}")]
    Network(#[from] reqwest::Error),
    #[error("Serialization error: {0}")]
    Serialization(#[from] serde_json::Error),
}

impl SupabaseClient {
    pub fn new(config: Config) -> Self {
        let db_client =
            Postgrest::new(&config.supabase_url).insert_header("apikey", &config.supabase_anon_key);

        let auth_client = SupabaseAuth::new(config.clone());

        Self {
            db_client,
            auth_client,
            config,
        }
    }

    /// Get a reference to the authentication client
    pub fn auth(&self) -> &SupabaseAuth { &self.auth_client }

    /// Get a database client with authentication headers
    pub fn database(&self) -> &Postgrest { &self.db_client }

    /// Get an authenticated database client using a bearer token
    pub fn authenticated_db(&self, access_token: &str) -> Postgrest {
        Postgrest::new(&self.config.supabase_url)
            .insert_header("apikey", &self.config.supabase_anon_key)
            .insert_header("Authorization", format!("Bearer {}", access_token))
    }

    /// Generic method to insert data into any table
    pub async fn insert<T>(
        &self,
        table: &str,
        data: &T,
        access_token: Option<&str>,
    ) -> Result<T, SupabaseError>
    where
        T: Serialize + for<'de> Deserialize<'de>,
    {
        let client = match access_token {
            Some(token) => self.authenticated_db(token),
            None => self.db_client.clone(),
        };

        let response = client
            .from(table)
            .insert(serde_json::to_string(data)?)
            .execute()
            .await?;

        let result: T = serde_json::from_str(&response.text().await?)?;
        Ok(result)
    }

    /// Generic method to select data from any table
    pub async fn select<T>(
        &self,
        table: &str,
        access_token: Option<&str>,
    ) -> Result<Vec<T>, SupabaseError>
    where
        T: for<'de> Deserialize<'de>,
    {
        let client = match access_token {
            Some(token) => self.authenticated_db(token),
            None => self.db_client.clone(),
        };

        let response = client.from(table).select("*").execute().await?;

        let result: Vec<T> = serde_json::from_str(&response.text().await?)?;
        Ok(result)
    }

    /// Generic method to update data in any table
    pub async fn update<T>(
        &self,
        table: &str,
        data: &T,
        filter: &str,
        access_token: Option<&str>,
    ) -> Result<T, SupabaseError>
    where
        T: Serialize + for<'de> Deserialize<'de>,
    {
        let client = match access_token {
            Some(token) => self.authenticated_db(token),
            None => self.db_client.clone(),
        };

        let response = client
            .from(table)
            .update(serde_json::to_string(data)?)
            .eq(filter.split('=').next().unwrap(), filter.split('=').nth(1).unwrap())
            .execute()
            .await?;

        let result: T = serde_json::from_str(&response.text().await?)?;
        Ok(result)
    }

    /// Generic method to delete data from any table
    pub async fn delete(
        &self,
        table: &str,
        filter: &str,
        access_token: Option<&str>,
    ) -> Result<(), SupabaseError> {
        let client = match access_token {
            Some(token) => self.authenticated_db(token),
            None => self.db_client.clone(),
        };

        client
            .from(table)
            .delete()
            .eq(filter.split('=').next().unwrap(), filter.split('=').nth(1).unwrap())
            .execute()
            .await?;

        Ok(())
    }

    /// Execute a stored procedure/function (requires appropriate permissions)
    pub async fn rpc(
        &self,
        function_name: &str,
        params: Option<HashMap<String, serde_json::Value>>,
        access_token: Option<&str>,
    ) -> Result<serde_json::Value, SupabaseError> {
        let client = match access_token {
            Some(token) => self.authenticated_db(token),
            None => self.db_client.clone(),
        };

        let params_str = match params {
            Some(params) => serde_json::to_string(&params)?,
            None => "{}".to_string(),
        };

        let response = client.rpc(function_name, params_str).execute().await?;

        let result: serde_json::Value = serde_json::from_str(&response.text().await?)?;
        Ok(result)
    }
}
