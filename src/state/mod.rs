use dioxus::{
    logger::tracing::debug,
    prelude::spawn,
};
pub use dioxus_radio::prelude::RadioReducer;
use dioxus_radio::prelude::*;
use serde::{
    Deserialize,
    Serialize,
};

use crate::models::{
    activity::{
        Activity,
        ActivityType,
    },
    dogs::Dog,
    food::Food,
    theme::Theme,
    user::UserProfile,
};


#[derive(<PERSON>lone, Debug, PartialEq, Deserialize, Serialize)]
pub struct AppState {
    pub debug_mode:      bool, // To track debug mode
    pub dogs:            Vec<Dog>,
    pub language:        String, // To track the selected language
    pub preferred_foods: Vec<Food>,
    pub selected_dog_id: Option<String>, // To track the currently selected pet
    pub theme:           Theme,
    pub treats_count:    u32,
    pub user:            UserProfile,
}

impl Default for AppState {
    fn default() -> Self {
        Self {
            debug_mode:      false,
            dogs:            vec![Dog::default()],
            language:        "en-US".to_string(),
            preferred_foods: vec![],
            selected_dog_id: None,
            theme:           Theme::Light,
            treats_count:    0,
            user:            UserProfile::default(),
        }
    }
}

impl AppState {
    fn get_selected_dog_index(&self) -> Option<usize> {
        if self.dogs.is_empty() {
            return None;
        }
        if self.selected_dog_id.is_none() {
            return Some(0);
        }

        let selected_dog_id = self.selected_dog_id.as_ref().unwrap();
        self.dogs.iter().position(|d| selected_dog_id.eq(&d.id))
    }

    pub fn get_selected_dog(&self) -> Option<&Dog> {
        if self.dogs.is_empty() {
            return None;
        }

        self.dogs.get(self.get_selected_dog_index().unwrap_or(0))
    }

    pub fn get_selected_dog_mut(&mut self) -> Option<&mut Dog> {
        if self.dogs.is_empty() {
            return None;
        }

        let index = self.get_selected_dog_index().unwrap_or(0);
        self.dogs.get_mut(index)
    }

    // pub fn select_dog(&mut self, dog: &Dog) { self.set_selected_dog_id(&dog.id); }

    pub fn set_selected_dog_id(&mut self, id: &str) { self.selected_dog_id = Some(id.to_string()); }
}

#[derive(PartialEq, Eq, Clone, Debug, Copy, Hash)]
pub enum AppEvent {
    ActivityStateUpdated,
    DebugModeToggled,
    DogUpdated,
    LanguageSelected,
    NotificationsToggle,
    PreferredFoodUpdate,
    ThemeSelected,
    TreatsUpdated,
    UserAuthUpdated,
    UserUpdated,
}
impl RadioChannel<AppState> for AppEvent {
    fn derive_channel(self, _radio: &AppState) -> Vec<Self> {
        let mut channel = vec![self];
        if self == Self::NotificationsToggle {
            channel.push(Self::UserUpdated);
        }
        channel
    }
}


pub fn use_state_by_event(event: AppEvent) -> Radio<AppState, AppEvent> {
    use_radio::<AppState, AppEvent>(event)
}
pub fn use_state() -> RadioStation<AppState, AppEvent> { use_radio_station::<AppState, AppEvent>() }
pub fn app_state_init() { use_init_radio_station::<AppState, AppEvent>(AppState::default); }

pub enum AppAction {
    ActivityToggle,
    ActivityTick,
    DebugModeToggle { debug_mode: bool },
    DogSelect { id: String },
    DogUpdate { dog: Dog },
    LanguageSelect { language: String },
    NotificationsToggle { enabled: bool },
    PreferredFoodUpdate { food: Food },
    ThemeSelect { theme: Theme },
    TreatsUpdate { treats: u32 },
    UserLogin,
    UserLogout,
    UserUpdate { user: UserProfile },
}

impl DataReducer for AppState {
    type Action = AppAction;
    type Channel = AppEvent;

    fn reduce(&mut self, action: Self::Action) -> ChannelSelection<Self::Channel> {
        match action {
            AppAction::ActivityToggle => {
                if let Some(dog) = self.get_selected_dog_mut() {
                    if dog.is_active() {
                        // * Stop activity
                        dog.end_activity();
                    } else {
                        // * Start activity
                        dog.start_activity(ActivityType::Walking);
                        // * Start timer to tick once a second
                        spawn(async move {
                            let mut state = use_state_by_event(AppEvent::ActivityStateUpdated);
                            loop {
                                tokio::time::sleep(std::time::Duration::from_secs(1)).await;
                                if let Some(dog) = state.read().get_selected_dog()
                                    && !dog.is_active()
                                {
                                    break;
                                }
                                state.apply(AppAction::ActivityTick);
                            }
                        });
                    }
                    return ChannelSelection::Select(AppEvent::ActivityStateUpdated);
                }
                ChannelSelection::Silence
            },
            AppAction::ActivityTick => ChannelSelection::Select(AppEvent::ActivityStateUpdated),
            AppAction::DebugModeToggle {
                debug_mode,
            } => {
                self.debug_mode = debug_mode;
                ChannelSelection::Select(AppEvent::DebugModeToggled)
            },
            AppAction::DogSelect {
                id,
            } => {
                self.set_selected_dog_id(&id);
                ChannelSelection::Select(AppEvent::DogUpdated)
            },
            AppAction::DogUpdate {
                dog,
            } => {
                let index = self.dogs.iter().position(|d| d.id == dog.id).unwrap();
                self.dogs[index] = dog;
                ChannelSelection::Select(AppEvent::DogUpdated)
            },
            AppAction::LanguageSelect {
                language,
            } => {
                self.language = language;
                ChannelSelection::Select(AppEvent::LanguageSelected)
            },
            AppAction::NotificationsToggle {
                enabled,
            } => {
                self.user.notifications_enabled = enabled;
                ChannelSelection::Select(AppEvent::NotificationsToggle)
            },
            AppAction::PreferredFoodUpdate {
                food,
            } => {
                let index = self.dogs.iter().position(|d| d.id == food.id).unwrap();
                self.dogs[index].preferred_foods = vec![food];
                ChannelSelection::Select(AppEvent::PreferredFoodUpdate)
            },
            AppAction::ThemeSelect {
                theme,
            } => {
                self.theme = theme;
                ChannelSelection::Select(AppEvent::ThemeSelected)
            },
            AppAction::TreatsUpdate {
                treats,
            } => {
                self.treats_count = treats;
                ChannelSelection::Select(AppEvent::TreatsUpdated)
            },
            AppAction::UserLogin => {
                // TODO: Implement actual login logic
                // For now, just set a dummy user ID to indicate authentication
                self.user.id = "temp_user_id".to_string();
                ChannelSelection::Select(AppEvent::UserAuthUpdated)
            },
            AppAction::UserLogout => {
                // Reset user profile to default state (empty ID = not authenticated)
                self.user = UserProfile::default();
                ChannelSelection::Select(AppEvent::UserAuthUpdated)
            },
            AppAction::UserUpdate {
                user,
            } => {
                self.user = user;
                ChannelSelection::Select(AppEvent::UserUpdated)
            },
        }
    }
}
