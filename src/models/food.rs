use chrono::{
    DateTime,
    NaiveDate,
    Utc,
};
use serde::{
    Deserialize,
    Serialize,
};

#[derive(Clone, PartialEq, Debug, Deserialize, Serialize)]
pub enum FoodType {
    Dry,
    Wet,
    Raw,
    Treat,
    Supplement,
}

impl Default for FoodType {
    fn default() -> Self { FoodType::Dry }
}

#[derive(Clone, PartialEq, Debug, Deserialize, Serialize)]
pub struct Food {
    pub id:                 String, // UUID
    pub name:               String,
    pub brand:              String,
    pub food_type:          FoodType,
    pub calories_per_100g:  u32,
    pub calories_per_cup:   Option<u32>, // for dry food
    pub protein_percentage: Option<f32>,
    pub fat_percentage:     Option<f32>,
    pub carb_percentage:    Option<f32>,
    pub fiber_percentage:   Option<f32>,
    pub image_url:          Option<String>,
    pub barcode:            Option<String>, // for food label scanning
    pub is_verified:        bool,           // verified by admin
    pub created_at:         Option<DateTime<Utc>>,
    pub updated_at:         Option<DateTime<Utc>>,
}

impl Default for Food {
    fn default() -> Self {
        Self {
            id:                 String::new(),
            name:               String::new(),
            brand:              String::new(),
            food_type:          FoodType::default(),
            calories_per_100g:  0,
            calories_per_cup:   None,
            protein_percentage: None,
            fat_percentage:     None,
            carb_percentage:    None,
            fiber_percentage:   None,
            image_url:          None,
            barcode:            None,
            is_verified:        false,
            created_at:         None,
            updated_at:         None,
        }
    }
}

#[derive(Clone, PartialEq, Debug, Deserialize, Serialize)]
pub struct DogPreferredFood {
    pub id:           String, // UUID
    pub dog_id:       String, // UUID reference
    pub food_id:      String, // UUID reference
    pub started_date: Option<NaiveDate>,
    pub ended_date:   Option<NaiveDate>,
    pub is_current:   bool,
    pub created_at:   Option<DateTime<Utc>>,
}

impl Default for DogPreferredFood {
    fn default() -> Self {
        Self {
            id:           String::new(),
            dog_id:       String::new(),
            food_id:      String::new(),
            started_date: None,
            ended_date:   None,
            is_current:   true,
            created_at:   None,
        }
    }
}

// Legacy structure for backward compatibility with existing UI code
#[derive(Clone, PartialEq, Eq, Debug, Deserialize, Serialize)]
pub struct FoodHistoryEntry {
    pub food_id:    String,
    pub name:       String,
    pub brand:      String,
    pub start_date: String,         // Using String for simplicity in UI
    pub end_date:   Option<String>, // Using String for simplicity in UI
}
