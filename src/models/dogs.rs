use std::{
    fmt::Display,
    ops::Deref,
};

use chrono::{
    DateTime,
    Duration,
    NaiveDate,
    Utc,
};
use serde::{
    Deserialize,
    Serialize,
};

use crate::{
    i18n::t,
    models::*,
};

#[derive(Clone, PartialEq, Debug, Deserialize, Serialize)]
pub enum Gender {
    Male,
    Female,
}

#[derive(<PERSON><PERSON>, PartialEq, Debug, Deserialize, Serialize)]
pub enum DogSize {
    Small,
    Medium,
    Large,
}

impl Default for DogSize {
    fn default() -> Self { DogSize::Medium }
}

impl Display for DogSize {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            DogSize::Small => write!(f, "Small"),
            DogSize::Medium => write!(f, "Medium"),
            DogSize::Large => write!(f, "Large"),
        }
    }
}

impl TryFrom<String> for DogSize {
    type Error = &'static str;

    fn try_from(value: String) -> Result<Self, Self::Error> {
        match value.trim().to_lowercase().as_str() {
            "small" => Ok(DogSize::Small),
            "medium" => Ok(DogSize::Medium),
            "large" => Ok(DogSize::Large),
            _ => Err("Invalid size"),
        }
    }
}

#[derive(Clone, PartialEq, Debug, Deserialize, Serialize)]
pub struct Breed {
    pub id:                 u32, // UUID
    pub name:               String,
    pub image_url:          Option<String>,
    pub male_weight_min:    Option<f32>,
    pub male_weight_max:    Option<f32>,
    pub female_weight_min:  Option<f32>,
    pub female_weight_max:  Option<f32>,
    pub typical_activities: Option<Vec<String>>, // JSON array of activity types
    pub preferred_foods:    Option<Vec<String>>, // JSON array of food types
    pub size_category:      DogSize,
    pub created_at:         Option<DateTime<Utc>>,
    pub updated_at:         Option<DateTime<Utc>>,
}

impl Default for Breed {
    fn default() -> Self {
        Self {
            id:                 0,
            name:               "Unknown".to_string(),
            image_url:          None,
            male_weight_min:    None,
            male_weight_max:    None,
            female_weight_min:  None,
            female_weight_max:  None,
            typical_activities: None,
            preferred_foods:    None,
            size_category:      DogSize::default(),
            created_at:         None,
            updated_at:         None,
        }
    }
}

#[derive(Clone, PartialEq, Debug, Deserialize, Serialize)]
pub struct Dog {
    pub id:                   String, // UUID
    pub user_id:              String, // UUID reference
    pub name:                 String,
    pub breed_id:             Option<String>, // UUID reference, optional
    pub gender:               Gender,
    pub birthday:             Option<NaiveDate>,
    pub size:                 DogSize,
    pub current_weight:       Option<f32>,
    pub target_weight:        Option<f32>,
    pub body_condition_score: Option<i32>, // 1-9 scale
    pub typical_activity:     Option<String>,
    pub photo_url:            Option<String>,
    pub created_at:           Option<DateTime<Utc>>,
    pub updated_at:           Option<DateTime<Utc>>,

    // These fields are not in database but calculated/fetched separately
    #[serde(skip)]
    pub activities:      Vec<Activity>, // Fetched from activities table
    #[serde(skip)]
    pub preferred_foods: Vec<Food>, // Fetched from dog_preferred_foods join
    #[serde(skip)]
    pub food_history:    Vec<FoodHistoryEntry>, // Legacy UI compatibility

    // Computed fields for UI compatibility
    #[serde(skip)]
    pub daily_calorie_goal:  Option<u32>,
    #[serde(skip)]
    pub daily_activity_goal: Option<u32>,
    #[serde(skip)]
    pub calorie_progress:    Option<u32>,
}

impl Default for Dog {
    fn default() -> Self {
        Self {
            id: "1".to_string(),
            user_id: "user-1".to_string(),
            name: "Rex Labrador".to_string(),
            breed_id: None,
            gender: Gender::Male,
            birthday: Some(NaiveDate::from_ymd_opt(2021, 4, 1).unwrap()),
            size: DogSize::Large,
            current_weight: Some(30.0),
            target_weight: Some(28.0),
            body_condition_score: Some(7),
            typical_activity: Some("walking".to_string()),
            photo_url: Some(
                "https://images.pexels.com/photos/3687770/pexels-photo-3687770.jpeg?auto=compress&cs=tinysrgb&w=400&h=400&dpr=1".to_string(),
            ),
            created_at: None,
            updated_at: None,

            // Non-database fields
            activities: vec![],
            preferred_foods: vec![],
            food_history: vec![],
            daily_calorie_goal: Some(2000),
            daily_activity_goal: Some(30),
            calorie_progress: Some(0),
        }
    }
}

impl Dog {
    pub fn get_age(&self) -> String {
        self.birthday.as_ref().map_or_else(
            || t!("dog-profile.birthday-not-set"),
            |birthday| {
                let now = chrono::offset::Utc::now().date_naive();
                let age_days = now.signed_duration_since(*birthday).num_days();
                if age_days < 365 {
                    if age_days < 30 {
                        format!("{age_days} days")
                    } else {
                        format!("{} months", (age_days as f64 / 30.4) as i32)
                    }
                } else {
                    format!("{} years", age_days / 365)
                }
            },
        )
    }

    // Convenience methods for UI compatibility
    pub fn weight(&self) -> Option<DogWeight> { self.current_weight.map(DogWeight) }

    pub fn image(&self) -> Option<&str> { self.photo_url.as_deref() }

    pub fn breed(&self) -> String {
        // This would normally fetch breed name from breed_id
        // For now, return a placeholder or cached value
        self.breed_id
            .clone()
            .unwrap_or_else(|| "Unknown".to_string())
    }

    pub fn body_fit_state(&self) -> Option<u32> { self.body_condition_score.map(|bcs| bcs as u32) }

    pub fn is_active(&self) -> bool { self.activities.last().is_some_and(|a| a.end_time.is_none()) }

    pub fn get_current_activity_duration(&self) -> Duration {
        self.activities
            .last()
            .map_or_else(Duration::zero, Activity::duration)
    }

    pub fn get_total_activity_duration(&self) -> Duration {
        self.activities
            .iter()
            .map(super::activity::Activity::duration)
            .sum()
    }

    pub fn get_total_activity_duration_today(&self) -> Duration {
        let now = chrono::offset::Utc::now();
        self.activities
            .iter()
            .filter(|a| a.start_time.date_naive() == now.date_naive())
            .map(super::activity::Activity::duration)
            .sum()
    }

    pub fn get_total_activity_duration_this_week(&self) -> Duration {
        let now = chrono::offset::Utc::now();
        self.activities
            .iter()
            .filter(|a| a.start_time.date_naive() >= now.date_naive() - Duration::days(7))
            .map(super::activity::Activity::duration)
            .sum()
    }

    pub fn get_total_activity_duration_this_month(&self) -> Duration {
        let now = chrono::offset::Utc::now();
        self.activities
            .iter()
            .filter(|a| a.start_time.date_naive() >= now.date_naive() - Duration::days(30))
            .map(super::activity::Activity::duration)
            .sum()
    }

    pub fn get_total_activity_duration_this_year(&self) -> Duration {
        let now = chrono::offset::Utc::now();
        self.activities
            .iter()
            .filter(|a| a.start_time.date_naive() >= now.date_naive() - Duration::days(365))
            .map(super::activity::Activity::duration)
            .sum()
    }

    pub fn get_total_activity_duration_all_time(&self) -> Duration {
        self.activities
            .iter()
            .map(super::activity::Activity::duration)
            .sum()
    }

    pub fn get_total_activity_duration_for_period(
        &self,
        start: DateTime<Utc>,
        end: DateTime<Utc>,
    ) -> Duration {
        self.activities
            .iter()
            .filter(|a| a.start_time >= start && a.start_time <= end)
            .map(super::activity::Activity::duration)
            .sum()
    }

    pub fn start_activity(&mut self, activity_type: ActivityType) {
        if self.is_active() {
            return;
        }
        self.activities.push(Activity {
            id: uuid::Uuid::new_v4().to_string(),
            dog_id: self.id.clone(),
            activity_type,
            start_time: chrono::offset::Utc::now(),
            end_time: None,
            duration_minutes: None,
            calories_burned: None,
            distance_meters: None,
            notes: None,
            created_at: Some(chrono::offset::Utc::now()),
            updated_at: Some(chrono::offset::Utc::now()),
        });
    }

    pub fn end_activity(&mut self) {
        if !self.is_active() {
            return;
        }
        self.activities.last_mut().unwrap().end_time = Some(chrono::offset::Utc::now());
    }
}


#[derive(Clone, PartialEq, Debug, Deserialize, Serialize, Copy)]
pub struct DogWeight(pub f32);
impl TryFrom<String> for DogWeight {
    type Error = &'static str;

    fn try_from(value: String) -> Result<Self, Self::Error> {
        match value.trim().parse() {
            Ok(v) => {
                if !(0.5..=150.0).contains(&v) {
                    return Err("Invalid weight");
                }
                Ok(Self(v))
            },
            Err(_) => Err("Invalid string format"),
        }
    }
}
// impl ToString for DogWeight {
//     fn to_string(&self) -> String { self.0.to_string() }
// }
impl Display for DogWeight {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result { write!(f, "{:.1}", self.0) }
}
impl Deref for DogWeight {
    type Target = f32;

    fn deref(&self) -> &Self::Target { &self.0 }
}
// impl From<f32> for DogWeight {
//     fn from(value: f32) -> Self { Self(value) }
// }
// impl From<u32> for DogWeight {
//     fn from(value: u32) -> Self { Self(value as f32) }
// }
// impl From<String> for DogWeight {
//     fn from(value: String) -> Self { DogWeight(value.parse().unwrap_or(0.0)) }
// }
// impl AsRef<f32> for DogWeight {
//     fn as_ref(&self) -> &f32 { &self.0 }
// }


#[derive(Clone, PartialEq, Eq, Debug)]
pub struct DogRecommendation {
    pub id:       String,
    pub text:     String,
    pub seen:     bool,
    pub category: String,
    // pub created_at: String, // Using String for simplicity
    // pub updated_at: String, // Using String for simplicity
}
