[package]
name = "scripts"
version = "0.1.0"
edition = "2024"
authors = ["Alexandr Priezzhev <<EMAIL>>"]

[dependencies]
chrono = { version = "0.4", features = ["serde"] }
fantoccini = "0.22.0"
headless_chrome = "1.0.17"
polars = { version = "0.50.0", features = ["ndarray", "lazy", "parquet"] }
reqwest = { version = "0.12.22", features = ["blocking"] }
scraper = "0.23.1"
select = "0.6"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
slog = "2.7.0"
slog-async = "2.8.0"
slog-term = "2.9.1"
thiserror = "2.0.12"
tokio = "1.47.1"
# tokio = { version = "1.0", features = ["time"] }


[profile.release]
opt-level = "z"
# opt-level = 3
debug = false
lto = "fat"
codegen-units = 1
panic = "abort"
strip = true
incremental = false

[profile.dev]
opt-level = 0
debug = 1
incremental = true
lto = "off"
codegen-units = 256
# TODO codegen-backend = "cranelift"

[profile.dev.package."*"]
opt-level = 1 # Page transitions do not work with 2 or 3
debug = 2

# * For build scripts and proc-macros.
[profile.dev.build-override]
opt-level = 3
