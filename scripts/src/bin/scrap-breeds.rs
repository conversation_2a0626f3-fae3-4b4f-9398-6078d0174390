#![feature(
    closure_lifetime_binder,
    coroutines,
    coroutine_trait,
    iterator_try_collect,
    generic_const_exprs,
    generic_const_items,
    // non_lifetime_binders,
    step_trait,
    stmt_expr_attributes,
    thread_id_value,
    try_blocks,
    type_alias_impl_trait,
)]

/// Scrap breeds from AKC and Purina UK websites and save them into a JSON file.
///
///
/// Here is the exact plan, do not add, remove or change anything:
///
///   (1) Access the American Kennel Club (AKC) website by iterating through pages from 1 to 25 (<https://www.akc.org/dog-breeds/page/{PAGE_NUMBER>}/, for example, for page 12 URL is <https://www.akc.org/dog-breeds/page/12>/) to compile a comprehensive list of dog breeds. Every page contains 12 breeds except the last one which has only 2, so in total we have 292 breeds.
///
///   (2) On the ACL website breeds list pages have a set of cards with a short breed information (breed image, name, description preview and "See More" link). Breed cards start after the alphabet section and are wrapped with a div with "breed-card-type-grid" class. Every card is wrapped in a div with "breed-type-card" class. The "See More" is a link to the breed details page (example, <https://www.akc.org/dog-breeds/manchester-terrier-standard>/ for "Manchester Terrier (Standard)").
///
///   (3) For each breed identified from the AKC website, extract breed name and image, then navigate to its dedicated breed details page (e.g., <https://www.akc.org/dog-breeds/nova-scotia-duck-tolling-retriever>/) and extract all basic, advanced and extra breed characteristics and details. Basic information (height, weight, life expectancy) is located in the top of the page right below the breed video/image section and is wrapped with a div with "breed-page__hero__overview__icon-block-wrap" class. Advanced information is presented below the basic one under the 'All Traits' tab (is accessible with a click on a respective tab title and is within a div with "breed-page__traits__all" Id in the page html code). Every advanced trait is in a div with the "breed-trait-group__trait breed-trait-group__padded breed-trait-group__row-wrap" class. Some details are collapsed by default and are accessible by tapping on "Read more" or a plus icon. Extra information pieces are in divs with a "breed-table__wrap" class. The content is available (appears with a help of JavaScript) only when clicking a plus icon for every details row. Without JavaScript extra details could be extracted from a "data-js-props" attribute value of a div with an attribute "data-js-component" with a value "breedPage".
///
///   (4) Access the Purina UK website by iterating through pages from 0 to 19 (<https://www.purina.co.uk/find-a-pet/dog-breeds?page=%2C{PAGE_NUMBER>}, for example, for page 17 URL i<https://www.purina.co.uk/find-a-pet/dog-breeds?page=%2C17>17) to compile a comprehensive list of dog breeds. Every page contains 12 breeds. 20 pages gives us 240 breeds in total. While URLs are numbered from 0 to 19, for page visitors pages are numbered from 1 to 20. This is an expected behavior.
///
///   (5) On the Purina UK website breeds list pages have a set of cards with a short breed information
/// (breed image and name). Every such a card itself is a link to a breed details page.
///
///   (6) For each breed identified from the Purina UK website, extract breed name and image, then navigate to its dedicated detail page (e.g., <https://www.purina.co.uk/find-a-pet/dog-breeds/mexican-hairless-medium>) and extract a breed description and all available basic and advanced breed characteristics and details. Decscription is in the top of the page below the breed image and name and is wrapped in a div with the "field--name-field-nppe-bs-description" class. Basic details go right below the description and are wrapped in a div with the "field--name-field-key-facts" class, each detail is in a separate div with the "key-facts-item" class. Advanced details wrapped in a div with the "field--name-field-c-subitems" class, each detail is in a separate div with the `field__item` class.
///
///   (7) Consolidate the lists of breed names from both sources, creating a master list of unique
/// breeds, ensuring that breeds found on only one website are also included.
///
///   (8) For each unique breed, synthesize all extracted details from both AKC and Purina, combining
/// and deduplicating any repeated information, and retaining all known details even if a breed is
/// present on only one source. In a case of details conflict between AKC and Purina, keep information
/// from both sources.
///
///   (9) Organize the consolidated and deduplicated breed data into a Parquet file, with each breed
/// name in the first column, followed by a columns with all identified breed traits and
/// characteristics. Put column titles in the first table row.
use std::collections::{
    BTreeMap,
    HashSet,
};
use std::{
    fmt::Display,
    sync::LazyLock,
};

use fantoccini::{
    ClientBuilder,
    Locator,
};
use headless_chrome::Browser;
use polars::prelude::*;
use reqwest::blocking::Client;
use scraper::{
    Html,
    Selector,
};
use slog::{
    Drain,
    debug,
    info,
};


/// Global logger
static LOG: LazyLock<slog::Logger> = LazyLock::new(|| {
    let decorator = slog_term::PlainSyncDecorator::new(std::io::stdout());
    let drain = slog_term::FullFormat::new(decorator).build().fuse();

    // let decorator = slog_term::TermDecorator::new().build();
    // let drain = slog_term::CompactFormat::new(decorator).build().fuse();
    // let drain = slog_async::Async::new(drain).build().fuse();

    slog::Logger::root(drain, slog::o!())
});


// * Models

#[derive(Debug, Clone, PartialEq, Eq, Hash)]
struct Breed {
    name:        String,
    details_url: Option<String>,
    image_url:   Option<String>,
}

impl Display for Breed {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(
            f,
            "{}: details: {}, image: {}",
            self.name,
            self.details_url.as_deref().unwrap_or(""),
            self.image_url.as_deref().unwrap_or("")
        )
    }
}

#[derive(Debug, Clone, PartialEq, Eq, Hash)]
struct BreedDetails {
    name:             String,
    image_url:        Option<String>,
    description:      Option<String>,
    basic_details:    BTreeMap<String, String>,
    advanced_details: BTreeMap<String, String>,
}


// * Main

#[tokio::main]
async fn main() {
    info!(LOG, "Starting.");

    info!(LOG, "Scraping AKC breeds.");
    let breeds_akc = scrap_akc_breeds();
    info!(LOG, "Found {} breeds on AKC.\n", &breeds_akc.len());

    info!(LOG, "Scraping AKC breed details.");
    let breed_details_akc = scrap_akc_breed_details(breeds_akc).await.unwrap();
    info!(LOG, "Found {} breeds on AKC.\n", breed_details_akc.len());

    info!(LOG, "Scraping Purina breeds.");
    let breeds_purina = scrap_purina_breeds();
    info!(LOG, "Found {} breeds on Purina.\n", &breeds_purina.len());

    info!(LOG, "Scraping Purina breed details.");
    let breed_details_purina = scrap_purina_breed_details(breeds_purina);
    info!(LOG, "Found {} breeds on Purina.\n", breed_details_purina.len());

    // info!(LOG, "Consolidating breeds.");
    // let breeds_consolidated = consolidate_breeds(&breeds_akc, &breeds_purina);
    // info!(LOG, "Found {} unique breeds.\n", breeds_consolidated.len());

    // info!(LOG, "Getting all breed details.");
    // let all_breed_details: HashSet<BreedDetails> = breed_details_akc
    //     .union(&breed_details_purina)
    //     .cloned()
    //     .collect();
    // info!(LOG, "Found {} breeds in total.\n", all_breed_details.len());

    // info!(LOG, "Consolidating breed details.");
    // let breeds = consolidate_breed_details(all_breed_details);
    // info!(LOG, "Found {} breeds in total.\n", breeds.len());

    // info!(LOG, "Saving breeds to Parquet.");
    // save_breeds_to_parquet(breeds);
    // info!(LOG, "Done.");
}


// * Scrapers

fn scrap_akc_breeds() -> Vec<Breed> {
    let client = Client::new();
    let mut breeds = Vec::new();
    for page in 1..=25 {
        let url = format!("https://www.akc.org/dog-breeds/page/{page}/");
        let response = client.get(&url).send().unwrap();
        let body = response.text().unwrap();
        let document = Html::parse_document(&body);
        let breed_selector = Selector::parse(".breed-type-card").unwrap();
        for element in document.select(&breed_selector) {
            let name = element
                .select(&Selector::parse("h3").unwrap())
                .next()
                .unwrap()
                .text()
                .next()
                .unwrap()
                .trim()
                .to_string();
            let details_url = element
                .select(&Selector::parse("a").unwrap())
                .next()
                .unwrap()
                .value()
                .attr("href")
                .unwrap()
                .to_string();
            let image_url = element
                .select(&Selector::parse("img").unwrap())
                .next()
                .unwrap()
                .value()
                .attr("data-src")
                .unwrap()
                .to_string();
            let breed = Breed {
                name:        name.clone(),
                details_url: Some(details_url.clone()),
                image_url:   Some(image_url.clone()),
            };
            // debug!(LOG, "Found breed: {}", breed);
            breeds.push(breed);
        }
    }
    breeds.sort_by_key(|b| b.name.clone());

    breeds
}

fn scrap_purina_breeds() -> Vec<Breed> {
    let client = Client::new();
    let mut breeds = Vec::new();
    for page in 0..=19 {
        let url = format!("https://www.purina.co.uk/find-a-pet/dog-breeds?page=%2C{page}");
        let response = client.get(&url).send().unwrap();
        let body = response.text().unwrap();
        let document = Html::parse_document(&body);
        // let breed_selector = Selector::parse(".results-view-image-container").unwrap();
        let breed_selector = Selector::parse(".result-animal-container").unwrap();
        let items = document.select(&breed_selector);
        // debug!(LOG, "Purina page {}, {} items found.", page, &items.count());
        for element in items {
            let name = element
                .select(&Selector::parse("h4").unwrap())
                .next()
                .unwrap()
                .select(&Selector::parse("a").unwrap())
                .next()
                .unwrap()
                .text()
                .next()
                .unwrap()
                .trim()
                .to_string();
            let details_url = element
                .select(&Selector::parse("a").unwrap())
                .next()
                .unwrap()
                .value()
                .attr("href")
                .unwrap()
                .to_string();
            let image_url = element
                .select(&Selector::parse("img").unwrap())
                .next()
                .unwrap()
                .value()
                .attr("src")
                .unwrap()
                .split('?')
                .next()
                .unwrap()
                .to_string();
            let breed = Breed {
                name:        name.clone(),
                details_url: Some("https://www.purina.co.uk/".to_string() + &details_url),
                image_url:   Some("https://www.purina.co.uk/".to_string() + &image_url),
            };
            // debug!(LOG, "Found breed: {}", breed);
            breeds.push(breed);
        }
    }
    breeds.sort_by_key(|b| b.name.clone());

    breeds
}

fn consolidate_breeds(set1: &HashSet<Breed>, set2: &HashSet<Breed>) -> HashSet<Breed> {
    let mut consolidated_breeds: HashSet<Breed> = HashSet::new();
    for breed in set1 {
        consolidated_breeds.insert(breed.clone());
    }
    for breed in set2 {
        consolidated_breeds.insert(breed.clone());
    }
    consolidated_breeds
}

async fn scrap_akc_breed_details(
    breeds: Vec<Breed>,
) -> Result<HashSet<BreedDetails>, fantoccini::error::CmdError> {
    // let client = Client::new();
    let client = ClientBuilder::native()
        .connect("http://localhost:4444")
        .await?;
    let mut breed_details = HashSet::new();
    for breed in breeds {
        let url = breed.details_url.clone().unwrap();

        // let browser = Browser::default().unwrap();
        // let tab = browser.new_tab().unwrap();
        // debug!(LOG, "[AKC] Scraping details for {} from {}", &breed.name, &url,);
        // tab.navigate_to(&url).unwrap();

        // let description_container = tab
        //     .wait_for_element(".breed-page__about__read-more__text")
        //     .unwrap();
        // let description_lines = description_container.find_elements("p").unwrap();
        // let description = if description_lines.is_empty() {
        //     description_container
        //         .get_inner_text()
        //         .unwrap()
        //         .trim()
        //         .to_string()
        // } else {
        //     description_lines
        //         .iter()
        //         .map(|element| element.get_inner_text().unwrap().trim().to_string())
        //         .collect::<Vec<String>>()
        //         .join("\n")
        // };
        // // debug!(LOG, "{} description: {}\n", breed.name, description);

        // let basic_details_container = tab
        //     .wait_for_element(".breed-page__hero__overview__icon-block-wrap")
        //     .unwrap();
        // let basic_details: BTreeMap<String, String> = basic_details_container
        //     .find_elements(".breed-page__hero__overview__icon-block")
        //     .unwrap()
        //     .iter()
        //     .map(|element| {
        //         let title = element
        //             .find_element("h3")
        //             .unwrap()
        //             .get_inner_text()
        //             .unwrap()
        //             .to_lowercase()
        //             .replace(' ', "_");
        //         let value = element.find_element("p").unwrap().get_inner_text().unwrap();
        //         (title, value)
        //     })
        //     .collect();
        // // debug!(LOG, "{} basic details: {:?}\n", breed.name, basic_details);

        // // // * Find "li.tabs__single-tab" element (there are a few), which has a
        // // // * "#tab__breed-page__traits__all" child. Then click on it.
        // // tab.wait_for_element("li.tabs__single-tab:has(#tab__breed-page__traits__all)")
        // //     .unwrap()
        // //     .click()
        // //     .unwrap();
        // // debug!(LOG, "{} clicked on the tab\n", breed.name);
        // let advanced_details_container = tab
        //     // .wait_for_element(".breed-trait-group__column")
        //     .wait_for_element("#breed-page__traits__all")
        //     .unwrap();
        // // debug!(LOG, "{} advanced details: {}\n", breed.name,
        // // _advanced_details);
        // let advanced_details: BTreeMap<String, String> = advanced_details_container
        //     .find_elements(".breed-trait-group__trait")
        //     .unwrap()
        //     .iter()
        //     .map(|element| {
        //         let title = element
        //             .find_element("h4.breed-trait-group__header")
        //             .unwrap()
        //             .get_inner_text()
        //             .unwrap()
        //             .to_lowercase()
        //             .replace(' ', "_");
        //         let is_percent = !element
        //             .find_elements(".breed-trait-score__score-unit")
        //             .unwrap()
        //             .is_empty();
        //         let value = if is_percent {
        //             (element
        //                 .find_elements(".breed-trait-score__score-unit--filled")
        //                 .unwrap()
        //                 .len() as f32
        //                 / 5.0)
        //                 .to_string()
        //         } else {
        //             element
        //                 .find_element(".breed-trait-score__choice--selected")
        //                 .unwrap()
        //                 .get_inner_text()
        //                 .unwrap_or_default()
        //                 .to_lowercase()
        //         };
        //         // let value = element
        //         //     .find_element("p.breed-trait-group__description-all")
        //         //     .unwrap()
        //         //     .get_inner_text()
        //         //     .unwrap();
        //         (title, value)
        //     })
        //     .collect();
        // // debug!(LOG, "{} advanced details: {:?}\n", breed.name, advanced_details);
        // tab.close(true).unwrap();

        client.goto(&url).await?;
        let description_container = client
            .find(Locator::Css(".breed-page__about__read-more__text"))
            .await?;
        let description_lines = description_container.find_all(Locator::Css("p")).await?;
        let description = if description_lines.is_empty() {
            description_container.text().await?
        } else {
            description_lines
                .iter()
                .map(async |element| element.text().await.unwrap())
                .collect::<Vec<String>>()
                .join("\n")
        };
        debug!(LOG, "{} description: {}\n", breed.name, description);
        let basic_details_container = client
            .find(Locator::Css(".breed-page__hero__overview__icon-block-wrap"))
            .await?;
        let basic_details: BTreeMap<String, String> = basic_details_container
            .find_all(Locator::Css(".breed-page__hero__overview__icon-block"))
            .await?
            .iter()
            .map(async |element| {
                let title = element
                    .find(Locator::Css("h3"))
                    .await
                    .unwrap()
                    .text()
                    .await
                    .unwrap()
                    .to_lowercase()
                    .replace(' ', "_");
                let value = element
                    .find(Locator::Css("p"))
                    .await
                    .unwrap()
                    .text()
                    .await
                    .unwrap();
                (title, value)
            })
            .collect();
        debug!(LOG, "{} basic details: {:?}\n", breed.name, basic_details);
        let advanced_details_container = client
            .find(Locator::Css("#breed-page__traits__all"))
            .await?;
        let advanced_details: BTreeMap<String, String> = advanced_details_container
            .find_all(Locator::Css(".breed-trait-group__trait"))
            .await?
            .iter()
            .map(async |element| {
                let title = element
                    .find(Locator::Css("h4.breed-trait-group__header"))
                    .await
                    .unwrap()
                    .text()
                    .await
                    .unwrap()
                    .to_lowercase()
                    .replace(' ', "_");
                let is_percent = !element
                    .find_all(Locator::Css(".breed-trait-score__score-unit"))
                    .await
                    .unwrap()
                    .is_empty();
                let value = if is_percent {
                    (element
                        .find_all(Locator::Css(".breed-trait-score__score-unit--filled"))
                        .await
                        .unwrap()
                        .len() as f32
                        / 5.0)
                        .to_string()
                } else {
                    element
                        .find(Locator::Css(".breed-trait-score__choice--selected"))
                        .await
                        .unwrap()
                        .text()
                        .await
                        .unwrap()
                        .to_lowercase()
                };
                (title, value)
            })
            .collect();
        debug!(LOG, "{} advanced details: {:?}\n", breed.name, advanced_details);

        breed_details.insert(BreedDetails {
            name: breed.name,
            image_url: breed.image_url,
            description: Some(description),
            basic_details,
            advanced_details,
        });
    }
    breed_details
}

fn scrap_purina_breed_details(breeds: Vec<Breed>) -> HashSet<BreedDetails> {
    let client = Client::new();
    let mut breed_details = HashSet::new();
    for breed in breeds {
        let url = breed.details_url.clone().unwrap();
        debug!(LOG, "[Purina] Scraping details for {} from {}", breed.name, &url,);
        let response = client.get(&url).send().unwrap();
        let body = response.text().unwrap();
        let document = Html::parse_document(&body);
        let description = document
            .select(&Selector::parse(".field--name-field-nppe-bs-description").unwrap())
            .next()
            .unwrap()
            .text()
            .next()
            .unwrap()
            .trim()
            .to_string();
        let basic_details_selector = Selector::parse(".field--name-field-key-facts").unwrap();
        let _basic_details = document
            .select(&basic_details_selector)
            .next()
            .unwrap()
            .text()
            .next()
            .unwrap()
            .trim()
            .to_string();
        let advanced_details_selector = Selector::parse(".field--name-field-c-subitems").unwrap();
        let _advanced_details = document
            .select(&advanced_details_selector)
            .next()
            .unwrap()
            .text()
            .next()
            .unwrap()
            .trim()
            .to_string();
        breed_details.insert(BreedDetails {
            name:             breed.name,
            image_url:        breed.image_url,
            description:      Some(description),
            basic_details:    BTreeMap::new(),
            advanced_details: BTreeMap::new(),
        });
    }
    breed_details
}

fn consolidate_breed_details(breeds: HashSet<BreedDetails>) -> HashSet<BreedDetails> {
    let mut consolidated_breeds = HashSet::new();
    for breed in breeds {
        consolidated_breeds.insert(breed);
    }
    consolidated_breeds
}

fn save_breeds_to_parquet(breeds: HashSet<BreedDetails>) {
    use std::fs::File;

    use polars::prelude::*;

    let mut names = Vec::new();
    let mut descriptions = Vec::new();
    let mut image_urls = Vec::new();

    for breed in breeds {
        names.push(breed.name);
        descriptions.push(breed.description.unwrap_or_else(String::new));
        image_urls.push(breed.image_url.unwrap_or_else(String::new));
    }

    let mut df = df! {
        "name" => names,
        "description" => descriptions,
        "image_url" => image_urls,
    }
    .unwrap();

    let mut file = File::create("breeds.parquet").unwrap();
    ParquetWriter::new(&mut file).finish(&mut df).unwrap();
}
