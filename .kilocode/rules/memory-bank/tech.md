# Technology Stack

This document outlines the technologies, dependencies, and development practices used in the My Dog in Fit project.

## 1. Core Frameworks & Languages

- **Rust:** The primary programming language, using the latest nightly version (1.90.0-nightly). The code is organized into a standard Cargo workspace.
- **Dioxus:** A UI framework for building cross-platform applications with Rust. The project uses version 0.7.0-alpha.3, targeting web, iOS, and Android platforms.
- **TailwindCSS:** A utility-first CSS framework (version 4) for styling the user interface.
- **Supabase:** The backend-as-a-service provider, utilized for:
  - **PostgreSQL:** Database for storing all application data.
  - **Auth:** User authentication and management.
  - **Storage:** Storing user-generated content like dog profile images.

## 2. Key Dependencies

### Rust / Cargo

- **`dioxus-router`:** For managing navigation and routing within the application.
- **`dioxus-i18n`:** For internationalization (i18n) support, enabling multi-language capabilities (EN, RU, SP).
- **`dioxus-motion`:** For creating animations and transitions in the UI.
- **`dioxus-storage`:** For accessing local device storage.
- **`postgrest`:** A client for interacting with the Supabase PostgreSQL database.
- **`serde`:** For serialization and deserialization of data structures.
- **`chrono`:** For handling dates and times.
- **`reqwest`:** For making HTTP requests, likely for the AI assistant feature.

### JavaScript / Node.js

- **`sharp`:** Used in scripts for image processing for generating app icons and assets.

## 3. Development & Build Tools

- **`cargo-script`:** Used for running various development and build scripts defined in `Cargo.toml`.
- **`dx` (Dioxus CLI):** The command-line interface for building and serving Dioxus applications on different platforms.
- **Android Emulator & iOS Simulator:** Used for testing and debugging on mobile platforms.

## 4. Development Environment

- The project is set up to be developed and run on a Linux environment.
- The `.gitignore` file is configured to exclude standard Rust and Node.js build artifacts, as well as editor-specific files.
- Linting is configured in `Cargo.toml` to enforce code quality and safety standards.
