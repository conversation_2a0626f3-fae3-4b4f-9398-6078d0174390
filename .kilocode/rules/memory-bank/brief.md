# My Dog In Fit

1. Purpose

   Enable dog owners to track nutrition, exercise, and health metrics
   via a cross-platform mobile app to maintain optimal weight and fitness
   for their pets.

2. Scope and features

- Dog profile management (breed, gender, age, weight, target weight, preferred food)
- Daily meal logging & calorie calculation
- Timer based or (optional) fitness tracker based activity tracking (both duration
  and kind of activity)
- Weight history & analytics
- Push notifications & reminders
- Multi-language (EN, RU, SP)
- Calorie calculation variance ≤5 % vs. vet reference
- AI assistant (Gemini Flash 2.5 based) for personalized non-medical
  recommendations for nutrition, exercise and health
- All breeds supported (title, image, adult size and weight ranges for males and
  females, preferred activities and food)
- All dietary foods known with calories per kg, components ratios and
  descriptions
- Xiaomi Mi Band direct integration without integration with Mi Fit app

3. Technology stack

- Rust (latest nightly version, 1.90.0-nightly at the time of writing)
- Dioxus (latest alpha version, 0.7.0-alpha.3 at the time of writing)
- TailwindCSS 4
- Supabase (Postgres, Auth, Storage)

4. Major Deliverables

- Frontend UI/UX design & implementation (iOS, Android)
- Backend API implementation (Supabase)
- Backend API docs (OpenAPI)
